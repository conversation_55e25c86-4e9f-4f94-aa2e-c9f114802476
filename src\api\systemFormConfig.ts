import request from "@/utils/system/request"
import type {
  FormConfigInfoRes,
  ConfigInfoRes,
  SaveFormConfigParams,
  FormConfigEditParams,
  FormConfigSaveParams,
  FormConfigListParams,
  FormConfigListRes,
} from "@/type/systemFormConfig.type"
import type { ResponseList, ResponseMessage } from "@/type/index.type"
export function getFormConfigInfo(params: {
  id: number|string
}): Promise<FormConfigInfoRes> {
  return request({
    url: "/api/admin/system/form/temp/info",
    method: "GET",
    params: params,
  })
}
export function configInfo(params: { formId: number|string }): Promise<ConfigInfoRes> {
  return request({
    url: "/api/admin/system/config/info",
    method: "GET",
    params: params,
  })
}
/***@name 系统设置的提交保存*/
export function saveFormConfig(
  params: SaveFormConfigParams
): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/config/save/form",
    method: "post",
    data: params,
  })
}
/***@name 表单模板列表*/
export function getFormConfigList(
  params: FormConfigListParams
): Promise<ResponseList<FormConfigListRes[]>> {
  const data = {
    keywords: params.keywords,
    page: params.page,
    limit: params.limit,
  }
  return request({
    url: "/api/admin/system/form/temp/list",
    method: "GET",
    params: data,
  })
}
/***@name 创建表单模板 */
export function getFormConfigSave(
  params: FormConfigSaveParams
): Promise<ResponseMessage> {
  const data = {
    content: params.content,
    info: params.info,
    name: params.name,
  }
  return request({
    url: "/api/admin/system/form/temp/save",
    method: "POST",
    data: data,
  })
}
/***@name 修改表单模板 */
export function getFormConfigEdit(
  params: FormConfigEditParams
): Promise<ResponseMessage> {
  const data = {
    content: params.content,
    info: params.info,
    name: params.name,
  }
  let newparams = { id: params.id }
  return request({
    url: "/api/admin/system/form/temp/update",
    method: "POST",
    params: newparams,
    data: data,
  })
}
