import type {TreeCategroyRes} from '@/type/category.type'

export interface Category{
    id:number,
    name:string,
    path?:string
    pid:number,
    sort:number,
    status?:boolean,
    type?:number,
    extra?:string,
    url?:string,

}
export interface leftData {
    searchInput?: string,
    tree: {
        data: TreeCategroyRes[],
        defaultProps: {
            children: string,
            label: string
        }
    }
}
export interface Tree {
    id: number
    label: string
    children?: Tree[]
}
export interface categoryProps {
    value: string,
    label: string,
    children: string,
    checkStrictly: boolean,
    emitPath: boolean,
}
export interface TreeDialog {
    show: boolean,
    title: string,
    form: Category,
    categoryProps: categoryProps
}
export interface PictureObj{
    sattDir:string,
    attId:number,
    isSelect:boolean
}
export interface PictureTs{
    list:PictureObj[],
    checkList:PictureObj[]
}