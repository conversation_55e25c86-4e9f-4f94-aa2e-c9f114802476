<template>
  <!-- 全局 Toast 提示 -->
  <div v-if="toast.show" class="fixed top-[2vh] left-1/2 -translate-x-1/2 z-50 w-[90vw] max-w-[20rem] rounded-xl bg-gray-900/95 dark:bg-gray-50/95 text-white dark:text-black shadow-xl backdrop-blur-sm p-[2vh] animate-fade-in-down border border-gray-700/10 dark:border-gray-200/10">
    <p class="font-semibold text-[3.5vw] sm:text-[1rem] leading-tight">{{ toast.title }}</p>
    <p class="text-[3vw] sm:text-[0.875rem] opacity-90 mt-[0.5vh]">{{ toast.description }}</p>
  </div>

  <div class="w-full min-h-[100vh] bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800 font-sans overflow-x-hidden">
    <div class="mx-auto w-full max-w-[28rem] bg-white/80 dark:bg-black/80 backdrop-blur-sm min-h-[100vh]">

      <!-- 头部搜索区域 -->
      <header class="sticky top-0 z-10 bg-white/95 dark:bg-black/95 backdrop-blur-xl border-b border-gray-200/30 dark:border-gray-700/30 p-[3vw] sm:p-[1.5vh]">
        <div class="relative flex items-center mb-[2vh]">
          <!-- 返回按钮 -->
          <button @click="goBack" class="w-[11vw] h-[11vw] sm:w-[2.5rem] sm:h-[2.5rem] min-w-[2.75rem] min-h-[2.75rem] rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center transition-all duration-200 touch-manipulation">
            <svg class="w-[5vw] h-[5vw] sm:w-[1.25rem] sm:h-[1.25rem] text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
          </button>

          <!-- 标题 - 绝对居中 -->
          <h1 class="absolute left-1/2 transform -translate-x-1/2 text-[4.5vw] sm:text-[1.25rem] font-bold text-gray-900 dark:text-white truncate">产品搜索</h1>

          <!-- 右侧占位，保持对称 -->
          <div class="w-[11vw] h-[11vw] sm:w-[2.5rem] sm:h-[2.5rem] min-w-[2.75rem] min-h-[2.75rem] ml-auto"></div>
        </div>

        <!-- 搜索输入框 -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-[3vw] sm:pl-3 flex items-center pointer-events-none">
            <svg class="h-[4vw] w-[4vw] sm:h-5 sm:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
            v-model="searchKeyword"
            @input="handleSearch"
            type="text"
            placeholder="输入产品名称或编码搜索..."
            class="w-full pl-[12vw] sm:pl-10 pr-[3vw] sm:pr-4 py-[3vw] sm:py-3 border border-gray-200/60 dark:border-gray-600/60 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 text-[3.5vw] sm:text-base"
          />
        </div>
      </header>

      <!-- 主体内容区域 -->
      <main class="p-[3vw] sm:p-[1.5vh] pb-[15vh] sm:pb-[8vh]">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex flex-col items-center justify-center py-[8vh] space-y-[2vh]">
          <div class="relative">
            <div class="w-[12vw] h-[12vw] sm:w-[3rem] sm:h-[3rem] rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse"></div>
            <svg class="absolute inset-0 h-[12vw] w-[12vw] sm:h-[3rem] sm:w-[3rem] animate-spin text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </div>
          <span class="text-gray-600 dark:text-gray-400 font-medium text-[3.5vw] sm:text-base">搜索中...</span>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!searchKeyword.trim()" class="flex flex-col items-center justify-center py-[12vh] text-center px-[5vw]">
          <div class="w-[16vw] h-[16vw] sm:w-[4rem] sm:h-[4rem] rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mb-[3vh]">
            <svg class="w-[8vw] h-[8vw] sm:w-[2rem] sm:h-[2rem] text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <h3 class="text-[4vw] sm:text-[1.125rem] font-semibold text-gray-900 dark:text-white mb-[1vh]">开始搜索产品</h3>
          <p class="text-gray-600 dark:text-gray-400 text-[3.2vw] sm:text-base leading-relaxed">输入产品名称或编码来查找产品</p>
        </div>

        <!-- 无搜索结果 -->
        <div v-else-if="!loading && productList.length === 0" class="flex flex-col items-center justify-center py-[12vh] text-center px-[5vw]">
          <div class="w-[16vw] h-[16vw] sm:w-[4rem] sm:h-[4rem] rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-[3vh]">
            <svg class="w-[8vw] h-[8vw] sm:w-[2rem] sm:h-[2rem] text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
          </div>
          <h3 class="text-[4vw] sm:text-[1.125rem] font-semibold text-gray-900 dark:text-white mb-[1vh]">未找到相关产品</h3>
          <p class="text-gray-600 dark:text-gray-400 text-[3.2vw] sm:text-base leading-relaxed">请尝试其他关键词</p>
        </div>

        <!-- 产品列表 -->
        <div v-else class="space-y-[2vh]">
          <div
            v-for="product in productList"
            :key="product.id"
            class="group rounded-xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900/90 dark:to-gray-800/50 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-200/30 dark:border-gray-700/30 backdrop-blur-sm"
          >
            <div class="p-[4vw] sm:p-[1.5vh]">
              <!-- 产品信息 -->
              <div class="flex items-center gap-[3vw] sm:gap-[1.25vw] mb-[3vh] sm:mb-[1vh]">
                <div class="h-[16vw] w-[16vw] sm:h-[4rem] sm:w-[4rem] min-h-[3.5rem] min-w-[3.5rem] rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 overflow-hidden shadow-sm border border-gray-200/30 dark:border-gray-600/30 flex-shrink-0">
                  <imagePreview :imgurl="product.image" :width="64" :height="64" :type="1" />
                </div>
                <div class="flex-1 space-y-[1vh] min-w-0">
                  <h3 class="text-[3.8vw] sm:text-[1rem] font-bold text-gray-900 dark:text-white leading-tight truncate">{{ product.productName || '未知产品' }}</h3>
                  <p class="text-[3vw] sm:text-[0.75rem] text-gray-600 dark:text-gray-400 truncate">编码: {{ product.attrValueNo || 'N/A' }}</p>
                  <p class="text-[3vw] sm:text-[0.75rem] text-gray-600 dark:text-gray-400 line-clamp-2">规格: {{ product.unitGroupStr || '暂无规格信息' }}</p>
                </div>
              </div>

              <!-- 操作按钮 -->
              <button
                @click="handleProductSelect(product)"
                class="w-full h-[12vw] sm:h-[2.75rem] min-h-[2.75rem] rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold transition-all duration-200 flex items-center justify-center gap-[2vw] sm:gap-[0.5vw] transform hover:scale-[1.01] active:scale-[0.99] touch-manipulation text-[3.5vw] sm:text-base"
              >
                <svg class="w-[4vw] h-[4vw] sm:w-[1rem] sm:h-[1rem]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
                出库/入库
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { getAttrValueList } from '@/api/product'
import imagePreview from '@/components/imagePreview/index.vue'

const router = useRouter()

// 状态定义
const searchKeyword = ref('')
const loading = ref(false)
const productList = ref<any[]>([])

// Toast 提示状态
const toast = reactive({
  show: false,
  title: '',
  description: ''
})

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 显示Toast提示
function showToastNotification(title: string, description: string) {
  toast.title = title
  toast.description = description
  toast.show = true
  setTimeout(() => {
    toast.show = false
  }, 3000)
}

// 处理搜索
async function handleSearch() {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(async () => {
    if (!searchKeyword.value.trim()) {
      productList.value = []
      return
    }
    
    await performSearch()
  }, 500) // 500ms 防抖
}

// 执行搜索
async function performSearch() {
  try {
    loading.value = true
    
    const params = {
      page: 1,
      limit: 50,
      keywords: searchKeyword.value.trim()
    }
    
    const response = await getAttrValueList(params)
    productList.value = response.list || []
    
  } catch (error: any) {
    console.error('搜索产品失败:', error)
    showToastNotification('搜索失败', error.message || '请稍后重试')
    productList.value = []
  } finally {
    loading.value = false
  }
}

// 处理产品选择
function handleProductSelect(product: any) {
  // 跳转到产品详情页面，传递产品编码
  router.push({
    path: '/qr-scanner/product-detail',
    query: { 
      id: product.id,
      from: 'search' // 标记来源为搜索
    }
  })
}

// 返回上一页
function goBack() {
  router.back()
}
</script>

<style scoped>
/* 自定义动画 */
@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-2vh) translateX(-50%);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  hyphens: auto;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移动端触摸优化 */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* 防止内容溢出 */
* {
  box-sizing: border-box;
  max-width: 100%;
}

/* 自定义滚动条 - 移动端友好 */
::-webkit-scrollbar {
  width: 0.25rem;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 0.125rem;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* 深色模式滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* 移动端特定样式 */
@media (max-width: 640px) {
  /* 确保输入框在移动端不会缩放 */
  input[type="text"] {
    font-size: 16px; /* 防止iOS缩放 */
    -webkit-appearance: none;
    border-radius: 0.5rem;
  }

  /* 移动端按钮优化 */
  button {
    -webkit-appearance: none;
    border: none;
    outline: none;
  }

  /* 防止文本选择 */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 小屏幕优化 */
  .space-y-\[2vh\] > * + * {
    margin-top: 1.5vh;
  }
}

/* 超小屏幕优化 (iPhone SE等) */
@media (max-width: 375px) {
  /* 进一步减小间距 */
  .p-\[3vw\] {
    padding: 2.5vw;
  }

  /* 调整字体大小 */
  .text-\[4\.5vw\] {
    font-size: 4vw;
  }

  .text-\[4vw\] {
    font-size: 3.8vw;
  }

  .text-\[3\.8vw\] {
    font-size: 3.5vw;
  }

  /* 调整图片大小 */
  .h-\[16vw\] {
    height: 14vw;
  }

  .w-\[16vw\] {
    width: 14vw;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .py-\[12vh\] {
    padding-top: 8vh;
    padding-bottom: 8vh;
  }

  .py-\[8vh\] {
    padding-top: 6vh;
    padding-bottom: 6vh;
  }

  .mb-\[3vh\] {
    margin-bottom: 2vh;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .shadow-md {
    box-shadow: 0 0.125rem 0.375rem -0.0625rem rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem -0.0625rem rgba(0, 0, 0, 0.06);
  }

  .shadow-lg {
    box-shadow: 0 0.25rem 0.5rem -0.125rem rgba(0, 0, 0, 0.1), 0 0.125rem 0.25rem -0.125rem rgba(0, 0, 0, 0.06);
  }
}
</style>
