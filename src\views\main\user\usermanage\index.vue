<template>
  <div class="layout-container">
    <div style="padding: 15px">
      <el-form label-position="right" label-width="100px">
        <el-form-item label="时间选择">
          <timepicker v-model="query.dateLimit" @change="getTableData(true)" />
        </el-form-item>
        <el-row>
          <el-form-item label="手机号">
            <el-input
              clearable
              @clear="getTableData(true)"
              v-model.trim="query.phone"
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="昵称">
            <el-input
              clearable
              @clear="getTableData(true)"
              v-model.trim="query.nickName"
              placeholder="请输入昵称"
            ></el-input>
          </el-form-item>
          <el-button
            type="primary"
            :icon="Search"
            class="search-btn"
            @click="getTableData(true)"
            style="margin-left: 10px"
          >
            {{ $t('message.common.search') }}
          </el-button>
        </el-row>
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-form
              label-position="left"
              inline
              class="demo-table-expand"
              style="padding: 0 10px"
            >
              <el-form-item label="性别:">
                <span v-if="props.row.sex == 0">未知</span>
                <span v-else-if="props.row.sex == 1">男</span>
                <span v-else-if="props.row.sex == 2">女</span>
                <span v-else>保密</span>
              </el-form-item>
              <el-form-item label="身份证号:">
                <span>{{ props.row.cardId }}</span>
              </el-form-item>
              <el-form-item label="用户备注:">
                <span>{{ props.row.mark }}</span>
              </el-form-item>
              <el-form-item label="最后登录ip:">
                <span>{{ props.row.lastIp }}</span>
              </el-form-item>
              <el-form-item label="状态:">
                <span>{{ props.row.status == 1 ? '正常' : '禁止' }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column prop="uid" label="用户ID" align="center" width="80" />
        <el-table-column label="头像" align="center" width="80">
          <template #default="scope">
            <imagePreview
              :imgurl="scope.row.avatar"
              :height="30"
              :width="30"
            ></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="nickName" label="昵称" align="center" />

        <el-table-column prop="money" label="余额" align="center" />
        <el-table-column prop="integral" label="积分" align="center" />

        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button type="primary" @click="openOperateFounds(scope.row)">
              修改金额
            </el-button>
          </template>
        </el-table-column>
      </Table>
      <UpdataMoneyLayer
        :layer="updataMoneyLayer"
        @getTableData="getTableData"
        v-if="updataMoneyLayer.show"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getUserList } from '@/api/userManage'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import Table from '@/components/table/index.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import type { UserListRes, UserListParams } from '@/type/userManage.type'
  import UpdataMoneyLayer from './updataMoneyLayer.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import timepicker from '@/components/timePicker/index.vue'
  // 存储搜索用的数据
  const query = reactive({
    phone: '',
    nickName: '',
    dateLimit: '',
    // dateLimit:''
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<UserListRes[]>([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: UserListParams = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getUserList(params)
      .then(res => {
        let data = res.list
        data.forEach((d: any) => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  getTableData(true)

  // 弹窗控制器
  const updataMoneyLayer: LayerInterface = reactive({
    row: {},
    show: false,
    title: '修改金额',
    showButton: true,
  })
  //弹出修改积分窗口
  const openOperateFounds = (row: object) => {
    updataMoneyLayer.row = row
    updataMoneyLayer.show = true
  }
  // 状态编辑功能
  const handleUpdateStatus = (row: any) => {
    if (!row.uid) {
      return
    }
    row.loading = true
    let params = {
      uid: row.uid,
      status: row.status,
    }

    // updateStatus(params)
    //   .then(res => {
    //     ElMessage({
    //       type: 'success',
    //       message: '状态变更成功'
    //     })
    //   })
    //   .catch(err => {
    //     row.status = !row.status
    //     ElMessage({
    //       type: 'error',
    //       message: '状态变更失败'
    //     })
    //   })
    //   .finally(() => {
    //     row.loading = false
    //   })
  }
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
  }

  .el-button--text {
    margin-right: 15px;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }
</style>
