<template>
    <Layer :layer="layer" ref="layerDom">
        <el-button type="primary" @click="handleAdd" style="margin-bottom: 10px;">添加</el-button>

        <el-table :data="tableList.list" border stripe v-loading="loading">
            <el-table-column prop="id" label="id"> </el-table-column>
            <el-table-column v-for="(item, index) in formConf.fields" :key="item.id" :label="item.__config__.label"
                :prop="item.__vModel__">
                <template #default="scope">
                    <el-image style="width: 36px; height: 36px" :src="picurl + scope.row[item.__vModel__]"
                        :preview-teleported="true" :preview-src-list="[(picurl + scope.row[item.__vModel__])]"
                        v-if="isImage(scope.row[item.__vModel__])" hide-on-click-modal />
                    <!-- v-if="" -->
                    <div v-else>
                        {{ scope.row[item.__vModel__] }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="sort" label="排序"> </el-table-column>
            <el-table-column label="状态"> <template #default="scope">
                    <div>
                        <el-tag type="success" effect="dark" v-if="scope.row.status">显示</el-tag>
                        <el-tag type="danger" effect="dark" v-else>隐藏</el-tag>
                    </div>
                </template></el-table-column>
            <el-table-column label="操作"> <template #default="scope">
                    <div>
                        <el-button @click="handleEdit(scope.row)">编辑</el-button>
                        <el-popconfirm title="确定删除选中的数据吗？" @confirm="del(scope.row)">
                            <template #reference>
                                <el-button type="danger">删除</el-button>
                            </template>
                        </el-popconfirm>
                    </div>
                </template></el-table-column>
        </el-table>

        <div style="display: flex;justify-content: center; margin-top: 10px;"> <el-pagination :total="tableList.total"
                :page-sizes="[10, 20, 40, 80]" layout="total, sizes, prev, pager, next, jumper"
                v-model:current-page="tableList.page" v-model:page-size="tableList.limit" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" background /></div>
        <Editlayer :layer="editlayer" @getTableData="getList" v-if="editlayer.show" />


    </Layer>
</template>
<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { ElFormItemContext } from 'element-plus/lib/el-form/src/token'
import { defineComponent, ref, reactive, defineProps, defineEmits } from 'vue'
import Layer from '@/components/layer/index.vue'
import { groupDataList, groupDataDelete } from '@/api/maintain'
import { getFormConfigInfo } from '@/api/systemFormConfig'
import { getPicUrl } from '@/utils/index'
import { ElMessage } from 'element-plus'
import Editlayer from '../combineddata/formlayerEdit.vue'
import { LayerInterface } from "@/components/layer/index.vue";
import { GroupDataListRes, SystemFormItemCheckRequest } from '@/type/maintain.type'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps({
    layer: {
        type: Object,
        default: () => {
            return {
                show: false,
                title: '',
                showButton: true
            }
        }
    }
})

const vfdRef = ref(null)
const ruleForm: Ref<ElFormItemContext | null> = ref(null)
const layerDom: Ref<LayerType | null> = ref(null)
const formConf = reactive<any>({ fields: [] })
const tableList = reactive({
    gid: props.layer.row.id,
    status: '',
    keywords: '',
    page: 1,
    limit: 20,
    list: [] as GroupDataListRes[],
    total: 0
})
const loading = ref(false)
const picurl = getPicUrl()
init()
function init() { // 用于判断新增还是编辑功能
    getInfo()
    getList()

}
function getInfo() {
    let obj = {
        id: props.layer.row.formId
    }
    getFormConfigInfo(obj).then(res => {
        let data = JSON.parse(res.content)
        formConf.fields = data.fields
        console.log(formConf.fields);

    })
}
function getList() {
    loading.value = true
    tableList.list = []
    groupDataList(tableList).then(res => {
        tableList.total = res.total
        let data = res.list
        data.forEach(item => {
            let obj = JSON.parse(item.value!)
            let newobj = {
                formId: obj.id,
                sort: obj.sort,
                status: obj.status,
                gid: item.gid,
                id: item.id
            }
            obj.fields.forEach((item: SystemFormItemCheckRequest) => {
                newobj[item.name] = item.value
            });
            tableList.list.push(newobj)
        })

    }).finally(() => {
        loading.value = false
    })
}
const dialogTableVisible = ref(false)
function del(row: { id: number }) {
    groupDataDelete(row).then(res => {
        getList()
        ElMessage({
            type: 'success',
            message: '操作成功'
        })
    })
}
// 弹窗控制器
const editlayer: LayerInterface = reactive({
    show: false,
    title: "新增",
    showButton: true,
});
// 编辑弹窗功能
const handleAdd = (row: any) => {

    row.formId = props.layer.row.formId
    row.gid = props.layer.row.id

    editlayer.title = "新增数据";
    editlayer.row = row;
    editlayer.show = true;
    editlayer.showButton = false;
}
// 编辑弹窗功能
const handleEdit = (row: any) => {
    row.formId = props.layer.row.formId
    editlayer.title = "编辑数据";
    editlayer.row = row;
    editlayer.show = true;
    editlayer.showButton = false;
}

const handleSizeChange = (val: number) => {
    tableList.limit = val
    getList()
}
const handleCurrentChange = (val: number) => {
    tableList.page = val
    getList()
}

const isImage = (val: string) => {
    if (!val) {
        return false
    }
    let index = val.lastIndexOf(".");
    let ext = val.substring(index + 1);
    return ['jpg', 'jpeg', 'gif', 'png', 'bmp', 'PNG', 'JPG', 'mp4'].
        indexOf(ext) !== -1;

}



</script>
  
<style lang="scss" scoped></style>