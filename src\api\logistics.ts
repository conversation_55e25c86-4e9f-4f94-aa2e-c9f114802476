import request from '@/utils/system/request'
import type { ResponseList, ResponseMessage } from '@/type/index.type'
import type { LogisticsTemplateRes,LogisticsTemplateFreeRes,LogisticsListParams,LogisticsListRes,CityTreeRes,LogisticsParams,LogisticsInfoRes } from '@/type/logistics.type'
//获取物流模板
export function getLogisticsTemplate(pram: {tempId:number}):Promise<LogisticsTemplateRes[]> {
    return request({
        url: '/api/admin/express/shipping/region/list',
        method: 'get',
        params: pram
    })
}
//获取物流模板  免费
export function getLogisticsTemplateFree(pram: {tempId:number}):Promise<LogisticsTemplateFreeRes[]> {
    return request({
        url: 'api/admin/express/shipping/free/list',
        method: 'get',
        params: pram
    })
}

//删除物流
export function deleteLogistics(pram: {id:number}):Promise<ResponseMessage> {
    return request({
        url: '/api/admin/express/shipping/templates/delete',
        method: 'get',
        params: pram
    })
}

//获取物流列表
export function getLogisticsList(pram: LogisticsListParams):Promise<ResponseList<LogisticsListRes[]>> {
    return request({
        url: '/api/admin/express/shipping/templates/list',
        method: 'GET',
        params: pram
    })
}


//获取城市树状图
export function getCityTree():Promise<CityTreeRes[]> {
    return request({
        url: '/api/admin/system/city/list/tree',
        method: 'GET'
    })
}

//新增物流
export function saveLogistics(pram: LogisticsParams):Promise<ResponseMessage> {
    return request({
        url: '/api/admin/express/shipping/templates/save',
        method: 'post',
        data: pram
    })
}
//修改物流
export function updateLogistics(pram: LogisticsParams):Promise<ResponseMessage> {
    return request({
        url: '/api/admin/express/shipping/templates/update',
        method: 'post',
        data: pram,
        params: { id: pram.id }
    })
}
//物流详情
export function logisticsInfo(pram: {id:number}):Promise<LogisticsInfoRes> {
    return request({
        url: '/api/admin/express/shipping/templates/info',
        method: 'get',
        params: pram
    })
}