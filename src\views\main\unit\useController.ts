import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { LayerInterface } from '@/components/layer/index.vue'
import type { RuleListRes } from '@/type/product.type'
import { getUnitGroupList, unitGroupDelete } from '@/api/units'
import { unitGroupSaveParams, UnitListParams } from '@/type/units.type'
import { Page } from '@/components/table/type'

/**
 * 单位管理控制器
 * @param type 单位类型：1-产品单位，2-原料单位
 */
export function useUnitController(type: number) {
  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
  })

  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
    width: '40%',
  })

  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })

  // 响应式数据
  const loading = ref(true)
  const tableData = ref<RuleListRes[]>([])
  const chooseData = ref([])

  // 处理表格选择变化
  const handleSelectionChange = (val: []) => {
    chooseData.value = val
  }

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
      type: type,
    } as UnitListParams & { type: number }

    getUnitGroupList(params)
      .then(res => {
        tableData.value = res.list
        page.total = Number(res.total)
      })
      .catch(() => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 删除功能
  const handleDel = (data: unitGroupSaveParams[]) => {
    let params = {
      ids: data
        .map(e => {
          return e.id
        })
        .join(','),
    }
    unitGroupDelete(params.ids).then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1)
    })
  }

  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }

  // 编辑弹窗功能
  const handleEdit = (row: RuleListRes) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }

  // 初始化数据
  const initData = () => {
    getTableData(true)
  }

  return {
    // 响应式数据
    query,
    layer,
    page,
    loading,
    tableData,
    chooseData,

    // 方法
    handleSelectionChange,
    getTableData,
    handleDel,
    handleAdd,
    handleEdit,
    initData,
  }
}