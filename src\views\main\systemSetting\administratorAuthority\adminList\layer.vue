<template>
  <Layer :layer="layer" @confirm="submit(ruleForm)" ref="layerDom">
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="120px"
      style="margin-right: 30px"
    >
      <el-form-item label="管理员账号：" prop="account">
        <el-input
          v-model="form.account"
          placeholder="请输入管理员账号："
        ></el-input>
      </el-form-item>
      <el-form-item label="管理员密码：" prop="pwd">
        <el-input
          v-model="form.pwd"
          :placeholder="layer.row ? '留空则不修改密码' : '请输入管理员密码'"
          type="password"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item label="管理员姓名：" prop="realName">
        <el-input
          v-model="form.realName"
          placeholder="请输入管理员姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="管理员身份：" prop="roles">
        <el-select
          v-model="form.roles"
          placeholder="选择管理员身份"
          style="width: 200px"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="管理区域：" prop="areaIds">
        <el-select
          v-model="form.areaIds"
          placeholder="选择管理区域"
          multiple
          style="width: 200px"
        >
          <el-option
            v-for="item in areaOptions"
            :key="item.id"
            :label="item.areaName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号：" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号"></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-switch
          v-model="form.status"
          :active-value="true"
          :inactive-value="false"
        />
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
  import type { LayerType } from '@/components/layer/index.vue'
  import type { Ref } from 'vue'
  import { defineComponent, ref, defineProps, reactive } from 'vue'
  import { getRoleList, saveAdmin, updateAdmin } from '@/api/systemSetting'
  import Layer from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import type { AdminAddParams } from '@/type/systemSetting.type'
  import { allAreaList } from '@/api/units'

  const props = defineProps({
    layer: {
      type: Object,
      default: () => {
        return {
          show: false,
          title: '',
          showButton: true,
        }
      },
    },
  })
  const emit = defineEmits(['getTableData'])
  const ruleForm = ref<FormInstance>()
  const layerDom: Ref<LayerType | null> = ref(null)
  let form = ref<any>({
    account: '',
    pwd: '',
    realName: '',
    roles: '',
    status: false,
    phone: '',
    areaIds: [],
  })
  const rules = reactive<FormRules>({
    account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
    pwd: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    roles: [{ required: true, message: '请输入身份', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    areaIds: [{ required: true, message: '请选择区域', trigger: 'blur' }],
  })

  // 动态更新密码校验规则
  const updatePasswordRules = () => {
    if (props.layer.row) {
      // 编辑模式：密码非必填
      rules.pwd = [{ required: false, message: '请输入密码', trigger: 'blur' }]
    } else {
      // 新增模式：密码必填
      rules.pwd = [{ required: true, message: '请输入密码', trigger: 'blur' }]
    }
  }
  const options = ref()
  const areaOptions = ref()

  init()
  function init() {
    // 用于判断新增还是编辑功能
    getRoleList({}).then(res => {
      options.value = res.list
    })
    allAreaList().then(res => {
      areaOptions.value = res
    })
    // 更新密码校验规则
    updatePasswordRules()

    if (props.layer.row) {
      form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
      form.value.roles = Number(form.value.roles)
      form.value.areaIds = form.value.areaIds.map(Number)
    } else {
    }
  }

  const submit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        let params = { ...form.value }

        // 编辑模式下，如果密码为空则不发送密码字段
        if (props.layer.row && !params.pwd) {
          delete params.pwd
        }

        if (props.layer.row) {
          updateForm(params)
        } else {
          addForm(params)
        }
      } else {
        console.log('error submit!', fields)
      }
    })
  }

  // 新增提交事件
  function addForm(params: AdminAddParams) {
    saveAdmin(params).then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功',
      })
      emit('getTableData', true)
      layerDom.value && layerDom.value.close()
    })
  }
  // 编辑提交事件
  function updateForm(params: AdminAddParams) {
    updateAdmin(params).then(res => {
      ElMessage({
        type: 'success',
        message: '编辑成功',
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
  }
</script>

<style lang="scss" scoped></style>
