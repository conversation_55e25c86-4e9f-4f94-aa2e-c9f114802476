<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h2 class="dialog-title">确认出库信息</h2>
        <p class="dialog-description">请确认以下出库信息无误，确认后将提交</p>
        <button class="close-button" @click="handleCancel">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="dialog-content">
        <!-- 基础信息 -->
        <div class="info-section">
          <h3 class="section-title">基础信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">电话:</span>
              <span class="info-value">{{ phoneNumber }}</span>
            </div>
            <div v-if="customerName" class="info-item">
              <span class="info-label">客户名称:</span>
              <span class="info-value">{{ customerName }}</span>
            </div>
            <div v-if="examineApprove" class="info-item">
              <span class="info-label">审批:</span>
              <span class="info-value">{{ examineApprove }}</span>
            </div>
            <div v-if="examine" class="info-item">
              <span class="info-label">审核:</span>
              <span class="info-value">{{ examine }}</span>
            </div>
            <div v-if="documentPreparation" class="info-item">
              <span class="info-label">制单:</span>
              <span class="info-value">{{ documentPreparation }}</span>
            </div>
            <div v-if="consignee" class="info-item">
              <span class="info-label">收货人:</span>
              <span class="info-value">{{ consignee }}</span>
            </div>
            <div v-if="driver" class="info-item">
              <span class="info-label">货车司机:</span>
              <span class="info-value">{{ driver }}</span>
            </div>
            <div v-if="remarks" class="info-item">
              <span class="info-label">备注:</span>
              <span class="info-value">{{ remarks }}</span>
            </div>
          </div>
        </div>

        <!-- 出库货品明细 -->
        <div class="products-section">
          <h3 class="section-title">出库货品明细</h3>
          <div class="table-container">
            <Table
              :data="products"
              :showPage="false"
              :showSelection="false"
              :showIndex="false"
            >
              <el-table-column prop="id" label="ID" align="center" width="80" />
              <el-table-column label="商品图" align="center" width="100">
                <template #default="scope">
                  <imagePreview :imgurl="scope.row.image"></imagePreview>
                </template>
              </el-table-column>
              <el-table-column
                prop="productName"
                label="商品名称"
                align="center"
              />
              <el-table-column
                prop="attrValueNo"
                label="产品编码"
                align="center"
              />
              <el-table-column prop="suk" label="商品规格" align="center" />
              <el-table-column
                prop="unitGroupStr"
                label="库存"
                align="center"
              />
              <el-table-column label="单价" align="center">
                <template #default="scope">
                  {{ formatPrice(scope.row.unitPrice) }}
                </template>
              </el-table-column>
              <el-table-column label="金额" align="center">
                <template #default="scope">
                  {{ formatPrice(scope.row.price) }}
                </template>
              </el-table-column>
              <el-table-column label="出库数量" align="center">
                <template #default="scope">
                  <div class="quantity-display">
                    <div
                      v-for="(unitName, index) in scope.row.unitNames"
                      :key="index"
                      class="quantity-item"
                    >
                      <span v-if="getUnitValue(scope.row, index) > 0">
                        {{ getUnitValue(scope.row, index) }}{{ unitName }}
                      </span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" width="200">
                <template #default="scope">
                  <span>{{ scope.row.logMark || '-' }}</span>
                </template>
              </el-table-column>
            </Table>
          </div>
        </div>

        <!-- 赠品货品明细 -->
        <div v-if="giftList.length > 0" class="products-section">
          <h3 class="section-title">赠品货品明细</h3>
          <div class="table-container">
            <Table
              :data="giftList"
              :showPage="false"
              :showSelection="false"
              :showIndex="false"
            >
              <el-table-column prop="id" label="ID" align="center" width="80" />
              <el-table-column label="商品图" align="center" width="100">
                <template #default="scope">
                  <imagePreview :imgurl="scope.row.image"></imagePreview>
                </template>
              </el-table-column>
              <el-table-column
                prop="productName"
                label="商品名称"
                align="center"
              />
              <el-table-column
                prop="attrValueNo"
                label="产品编码"
                align="center"
              />
              <el-table-column prop="suk" label="商品规格" align="center" />
              <el-table-column
                prop="unitGroupStr"
                label="库存"
                align="center"
              />
              <el-table-column label="单价" align="center">
                <template #default="scope">
                  <span style="color: #6b7280;">¥0.00（赠品）</span>
                </template>
              </el-table-column>
              <el-table-column label="金额" align="center">
                <template #default="scope">
                  <span style="color: #6b7280;">¥0.00（赠品）</span>
                </template>
              </el-table-column>
              <el-table-column label="出库数量" align="center">
                <template #default="scope">
                  <div class="quantity-display">
                    <div
                      v-for="(unitName, index) in scope.row.unitNames"
                      :key="index"
                      class="quantity-item"
                    >
                      <span v-if="getUnitValue(scope.row, index) > 0">
                        {{ getUnitValue(scope.row, index) }}{{ unitName }}
                      </span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" width="200">
                <template #default="scope">
                  <span>{{ scope.row.logMark || '-' }}</span>
                </template>
              </el-table-column>
            </Table>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="handleCancel">取消</button>
        <button class="btn btn-primary" @click="handleConfirm">确认提交</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits } from 'vue'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'

  // 定义 props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    phoneNumber: {
      type: String,
      required: true,
    },
    remarks: {
      type: String,
      default: '',
    },
    products: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 新增的字段
    customerName: {
      type: String,
      default: '',
    },
    examineApprove: {
      type: String,
      default: '',
    },
    examine: {
      type: String,
      default: '',
    },
    documentPreparation: {
      type: String,
      default: '',
    },
    consignee: {
      type: String,
      default: '',
    },
    driver: {
      type: String,
      default: '',
    },
    giftList: {
      type: Array,
      default: () => []
    },
  })

  // 定义 emits
  const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

  // 获取数量值的函数
  const getUnitValue = (row: any, index: number) => {
    // 优先使用 changeUnitNums
    if (
      row.changeUnitNums &&
      Array.isArray(row.changeUnitNums) &&
      row.changeUnitNums[index] !== undefined
    ) {
      return row.changeUnitNums[index]
    }
    // 回退到 unitNums
    if (
      row.unitNums &&
      Array.isArray(row.unitNums) &&
      row.unitNums[index] !== undefined
    ) {
      return row.unitNums[index]
    }
    return 0
  }

  // 格式化价格显示的函数
  const formatPrice = (price: number | string | undefined | null) => {
    if (price === null || price === undefined || price === '') {
      return '0.00'
    }
    const numPrice = typeof price === 'string' ? parseFloat(price) : price
    if (isNaN(numPrice)) {
      return '0.00'
    }
    return numPrice.toFixed(2)
  }

  // 处理取消
  const handleCancel = () => {
    emit('update:visible', false)
    emit('cancel')
  }

  // 处理确认
  const handleConfirm = () => {
    emit('confirm')
    emit('update:visible', false)
  }

  // 处理遮罩层点击
  const handleOverlayClick = () => {
    handleCancel()
  }
</script>

<style scoped>
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .dialog-container {
    background: white;
    border-radius: 12px;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 1200px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .dialog-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
  }

  .dialog-title {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px 0;
  }

  .dialog-description {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
  }

  .close-button:hover {
    color: #374151;
  }

  .dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }

  .info-section {
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #111827;
    margin: 0 0 16px 0;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .info-label {
    font-weight: 500;
    color: #374151;
    min-width: 60px;
    text-align: right;
  }

  .info-value {
    color: #111827;
    word-break: break-all;
  }

  .products-section {
    margin-bottom: 16px;
  }

  .table-container {
    max-height: 300px;
    overflow-y: auto;
  }

  .quantity-display {
    text-align: center;
  }

  .quantity-item {
    margin-bottom: 4px;
    font-weight: 500;
  }

  .quantity-item:last-child {
    margin-bottom: 0;
  }

  .dialog-footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
    min-width: 80px;
  }

  .btn-secondary {
    background-color: white;
    color: #374151;
    border-color: #d1d5db;
  }

  .btn-secondary:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .dialog-overlay {
      padding: 10px;
    }

    .dialog-container {
      max-height: 95vh;
    }

    .dialog-header,
    .dialog-content,
    .dialog-footer {
      padding-left: 16px;
      padding-right: 16px;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }

    .table-container {
      font-size: 12px;
    }
  }

  /* 滚动条样式 */
  .table-container::-webkit-scrollbar,
  .dialog-content::-webkit-scrollbar {
    width: 6px;
  }

  .table-container::-webkit-scrollbar-track,
  .dialog-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .table-container::-webkit-scrollbar-thumb,
  .dialog-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .table-container::-webkit-scrollbar-thumb:hover,
  .dialog-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
</style>
