<template>
  <div class="layout-container">
    <div style="padding: 15px">
      <el-form label-position="right" label-width="100px">
        <el-form-item label="时间选择">
          <timepicker v-model="query.dateLimit" @change="getTableData(true)" />
        </el-form-item>
        <Unfold>
          <el-row>
            <el-form-item label="充值方式">
              <el-radio-group
                v-model="query.payType"
                @change="getTableData(true)"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="alipay">支付宝</el-radio-button>
                <el-radio-button label="weixin">微信</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="充值目标">
              <el-radio-group
                v-model="query.rechargeTarget"
                @change="getTableData(true)"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button
                  v-for="(val, key) in rechargeTargetList"
                  :index="key"
                  :label="key"
                >
                  {{ val }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group
                v-model="query.status"
                @change="getTableData(true)"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="-1">取消</el-radio-button>
                <el-radio-button label="0">待支付</el-radio-button>
                <el-radio-button label="1">已支付</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="账号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.account"
                placeholder="请输入账号"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.phone"
                placeholder="请输入手机号"
              ></el-input>
            </el-form-item>
            <el-form-item label="昵称">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.nickname"
                placeholder="请输入昵称"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-form-item label="订单号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.orderNum"
                placeholder="请输入订单号"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-button
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </el-row>
        </Unfold>
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column prop="uid" label="用户id" align="center">
          <template #default="scope">
            <div>
              <RightMenu :uid="scope.row.uid"></RightMenu>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="nickName" label="昵称" align="center" />
        <el-table-column prop="orderNum" label="订单号" align="center" />
        <el-table-column prop="price" label="充值金额" align="center" />
        <el-table-column label="充值目标" align="center">
          <template #default="scope">
            <div>
              {{ rechargeTargetList[scope.row.rechargeTarget] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="充值方式" align="center">
          <template #default="scope">
            <div>
              <span v-if="scope.row.payType == 'alipay'">支付宝</span>
              <span v-if="scope.row.payType == 'weixin'">微信</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <div>
              <span v-if="scope.row.status == -1">取消</span>
              <span v-if="scope.row.status == 0">待支付</span>
              <span v-if="scope.row.status == 1">已支付</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="充值时间" align="center" />
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { rechargeTargetList } from '@/utils/contants'
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getRecordsList } from '@/api/financial'
  import Table from '@/components/table/index.vue'
  import { Search } from '@element-plus/icons-vue'
  import RightMenu from '@/components/rightMenu/index.vue'
  import Unfold from '@/components/unfold/index.vue'
  import timepicker from '@/components/timePicker/index.vue'
  import type { RecordsListRes } from '@/type/financial.type'
  // 存储搜索用的数据
  const query = reactive({
    account: '',
    phone: '',
    nickname: '',
    dateLimit: '',
    rechargeTarget: 0,
    orderNum: '',
    status: 0,
    payType: '',
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<RecordsListRes[]>([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getRecordsList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  getTableData(true)
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
