<template>
  <div class="layout-container">
    <div style="padding: 15px">
      <el-form label-position="right" label-width="100px">
        <el-form-item label="订单状态">
          <el-radio-group v-model="query.status" @change="getTableData(true)">
            <el-radio-button
              :label="key"
              v-for="(val, key) in orderStatus"
              :key="key"
            >
              {{ val }} {{ '(' + tabOptions[key] ? tabOptions[key] : 0 + ')' }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <Unfold>
          <el-form-item label="时间选择">
            <timepicker
              v-model="query.dateLimit"
              @change="getTableData(true)"
            />
          </el-form-item>
          <el-row>
            <el-form-item label="订单号">
              <el-input
                v-model="query.orderNum"
                placeholder="请输入订单号"
                clearable
                @clear="getTableData(true)"
              ></el-input>
            </el-form-item>
            <el-form-item label="账号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.account"
                placeholder="请输入账号"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.phone"
                placeholder="请输入手机号"
              ></el-input>
            </el-form-item>
            <el-form-item label="昵称">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.nickName"
                placeholder="请输入昵称"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-button
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
              style="margin-left: 15px"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </el-row>
        </Unfold>
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column type="expand" label="商品">
          <template #default="props">
            <div style="margin: 1rem">
              <el-table :data="props.row.productList" :border="false">
                <el-table-column label="商品图片" prop="name" align="center">
                  <template #default="scope">
                    <imgPreview :imgurl="scope.row.info.image"></imgPreview>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" align="center">
                  <template #default="scope">
                    {{ scope.row.info.productName }}
                  </template>
                </el-table-column>
                <el-table-column label="规格" align="center">
                  <template #default="scope">
                    {{ scope.row.info.sku }}
                  </template>
                </el-table-column>
                <el-table-column label="价格" align="center">
                  <template #default="scope">
                    {{
                      props.row.payChannelStr === '金币支付'
                        ? scope.row.info.goldPrice + '金币'
                        : scope.row.info.price + '元'
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="数量" align="center">
                  <template #default="scope">
                    {{ scope.row.info.payNum }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="id"
          label="订单ID"
          align="center"
          :show-overflow-tooltip="false"
        />
        <el-table-column
          prop="orderNum"
          label="订单号"
          align="center"
          :show-overflow-tooltip="false"
        />
        <el-table-column prop="account" label="账号" align="center" />
        <el-table-column prop="realName" label="用户姓名" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="nickName" label="昵称" align="center" />

        <el-table-column prop="payPrice" label="实际支付" align="center">
          <template #default="scope">
            <div>{{ scope.row.payPrice }}元</div>
          </template>
        </el-table-column>
        <el-table-column prop="payTypeStr" label="支付方式" align="center" />
        <el-table-column
          prop="statusStr.value"
          label="订单状态"
          align="center"
        />
        <el-table-column prop="createTime" label="下单时间" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            .
            <el-button @click="handleEdit(scope.row)">查看</el-button>
            <!-- <el-popconfirm :title="$t('message.common.delTip')" @confirm="handleDel([scope.row])">
              <template #reference>
                <el-button type="danger">{{ $t("message.common.del") }}</el-button>
              </template>
            </el-popconfirm> -->
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { fromList } from '@/utils/contants'
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getOrderList, getStatusNum } from '@/api/order'
  import { LayerInterface } from '@/components/layer/index.vue'
  import Table from '@/components/table/index.vue'
  import Layer from './layer.vue'
  import { Search } from '@element-plus/icons-vue'
  import imgPreview from '@/components/imagePreview/index.vue'
  import Unfold from '@/components/unfold/index.vue'
  import timepicker from '@/components/timePicker/index.vue'
  import type {
    StatusNumRes,
    OrderListParams,
    OrderListRes,
  } from '@/type/order.type'
  //订单状态
  interface orderStatus {
    [key: string]: string
  }
  //发货
  interface sendForm {
    expressName: string
    expressAbbreviation: string
    expressNumber: string
    orderNum: string
  }
  interface expressList {
    expressName: string
    expressAbbreviation: string
  }
  interface send {
    form: sendForm
    expressName: string
    show: boolean
    options: expressList[]
  }
  // 存储搜索用的数据
  const query = reactive({
    account: '',
    phone: '',
    nickName: '',
    orderNum: '',
    status: 'all',
    dateLimit: '',
    type: 2,
  })
  const orderStatus: orderStatus = {
    all: '全部',
    unPaid: '未支付',
    refunding: '退款中',
    refunded: '已退款',
    notShipped: '待发货',
    toBeWrittenOff: '待核销',
    spike: '待收货',
    bargain: '待评价',
    complete: '交易完成',
    deleted: '已删除',
  }

  //表头tab栏数据
  const tabOptions = ref<StatusNumRes>({})
  const getTabOptions = () => {
    getStatusNum(query).then(res => {
      tabOptions.value = res
    })
  }
  getTabOptions()
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: false,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<OrderListRes[]>([])

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: OrderListParams = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getOrderList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: any) => {
    // let params = {
    //   orderNum: data
    //     .map((e: any) => {
    //       return e.orderNum;
    //     })
    //     .join(","),
    // };
    // deleteOrder(params).then((res) => {
    //   ElMessage({
    //     type: "success",
    //     message: "删除成功",
    //   });
    //   getTableData(tableData.value.length === 1 ? true : false);
    // });
  }

  // 查看
  const handleEdit = (row: any) => {
    layer.title = '查看详情'
    layer.row = row
    layer.show = true
  }
  getTableData(true)
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .text_overflow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 400px;
  }
</style>
