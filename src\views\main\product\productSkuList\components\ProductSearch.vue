<template>
  <el-form label-width="80px" :inline="false">
    <el-form-item label-width="0">
      <el-form-item label="商品分类">
        <el-cascader
          :options="options"
          :props="cascaderProps"
          clearable
          :model-value="cateId"
          @update:model-value="updateCateId"
          @change="handleSearch"
        />
      </el-form-item>
      <el-form-item label="商品搜索">
        <div class="layout-container-form-search">
          <el-input
            :model-value="keywords"
            @update:model-value="updateKeywords"
            placeholder="请输入关键字"
            clearable
            @clear="handleSearch"
          ></el-input>
          <el-button
            type="primary"
            :icon="Search"
            class="search-btn"
            @click="handleSearch"
          >
            搜索
          </el-button>
        </div>
      </el-form-item>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
  import { Search } from '@element-plus/icons-vue'
  import type { TreeCategroyRes } from '@/type/category.type'

  defineProps<{
    keywords: string
    cateId: string
    options: TreeCategroyRes[]
  }>()

  const emit = defineEmits<{
    'update:keywords': [value: string]
    'update:cateId': [value: string]
    search: []
  }>()

  const cascaderProps = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }

  const updateKeywords = (value: string) => {
    emit('update:keywords', value)
  }

  const updateCateId = (value: string) => {
    emit('update:cateId', value)
  }

  const handleSearch = () => {
    emit('search')
  }
</script>

<style lang="scss" scoped>
  .layout-container-form-search {
    display: flex;
    gap: 10px;
  }

  .search-btn {
    flex-shrink: 0;
  }
</style>
