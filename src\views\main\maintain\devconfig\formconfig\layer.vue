<template>
  <Layer :layer="props.layer" ref="layerDom">
    <config-list :edit-data="layer.row" @getFormConfigDataResult="handlerGetFormConfigData" />
  </Layer>
</template>

<script lang="ts" setup>
import configList from '@/components/FormGenerator/index/Home.vue'
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import { defineComponent, ref } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import Layer from '@/components/layer/index.vue'
import {getFormConfigSave,getFormConfigEdit} from '@/api/systemFormConfig'
import type {FormConfigSaveParams,FormConfigEditParams} from '@/type/systemFormConfig.type'
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const emit = defineEmits(['hideDialog'])
const layerDom: Ref<LayerType | null> = ref(null)
function handlerGetFormConfigData(formConfigData: FormConfigEditParams) {
  formConfigData.id ? handlerEdit(formConfigData) : handlerSave(formConfigData)
}
function handlerSave(pram: FormConfigSaveParams) {
 getFormConfigSave(pram).then(data => {
    ElMessage({
      type: 'success',
      message: '创建表单配置成功'
    })
    setTimeout(() => {
      emit('hideDialog')
    }, 800)
  })
}
function handlerEdit(pram:FormConfigEditParams) {
  getFormConfigEdit(pram).then(data => {
    ElMessage({
      type: 'success',
      message: '编辑表单配置成功'
    })
    setTimeout(() => {
      emit('hideDialog')
    }, 800)
  })
}
</script>

<style lang="scss" scoped></style>