<template>
  <div class="system-table-box">
    <el-table
      v-bind="$attrs"
      ref="table"
      class="system-table"
      border
      height="100%"
      :data="data"
      @selection-change="handleSelectionChange"
      @row-contextmenu="handleRowContextMenu"
      :row-class-name="getRowClassName"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50"
        v-if="showSelection"
      />
      <el-table-column label="序号" width="60" align="center" v-if="showIndex">
        <template #default="scope">
          {{ (page.index - 1) * page.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <slot></slot>
    </el-table>
    <el-pagination
      v-if="showPage"
      v-model:current-page="page.index"
      class="system-page"
      background
      :layout="pageLayout"
      :total="page.total"
      :page-size="page.size"
      :page-sizes="pageSizes"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>

    <!-- 隐藏的右键菜单触发元素 -->
    <div
      ref="contextMenuTrigger"
      v-contextmenu:contextmenu="currentRow"
      style="
        position: absolute;
        left: -9999px;
        top: -9999px;
        width: 1px;
        height: 1px;
      "
    ></div>

    <!-- 右键菜单 -->
    <v-contextmenu ref="contextmenu" v-if="showContextMenu">
      <v-contextmenu-item
        v-for="item in contextMenuItems"
        :key="item.key"
        :class="{
          'delete-item': item.type === 'delete' || item.key === 'delete',
        }"
        @click="handleContextMenuClick(item)"
      >
        <el-icon v-if="item.icon">
          <component :is="item.icon" />
        </el-icon>
        {{ item.label }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, onActivated, onMounted } from 'vue'
  import { Page } from '@/components/table/type'
  import { directive, Contextmenu, ContextmenuItem } from 'v-contextmenu'
  import 'v-contextmenu/dist/themes/default.css'

  export default defineComponent({
    components: {
      'v-contextmenu': Contextmenu,
      'v-contextmenu-item': ContextmenuItem,
    },
    directives: {
      contextmenu: directive,
    },
    props: {
      data: { type: Array, default: () => [] }, // 数据源
      select: { type: Array, default: () => [] }, // 已选择的数据，与selection结合使用
      showIndex: { type: Boolean, default: false }, // 是否展示index选择，默认否
      showSelection: { type: Boolean, default: false }, // 是否展示选择框，默认否
      showPage: { type: Boolean, default: true }, // 是否展示页级组件，默认是
      showContextMenu: { type: Boolean, default: false }, // 是否显示右键菜单
      contextMenuItems: { type: Array, default: () => [] }, // 右键菜单项
      page: {
        // 分页参数
        type: Object,
        default: () => {
          return { index: 1, size: 20, total: 0 }
        },
      },
      pageLayout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper',
      }, // 分页需要显示的东西，默认全部
      pageSizes: { type: Array, default: [10, 20, 50, 100] },
    },
    setup(props, context) {
      const table: any = ref(null)
      const contextmenu = ref()
      const contextMenuTrigger = ref()
      const currentRow = ref<any>(null)
      const isContextMenuVisible = ref(false)
      let timer: any = null
      // 分页相关：监听页码切换事件
      const handleCurrentChange = (val: Number) => {
        if (timer) {
          props.page.index = 1
        } else {
          props.page.index = val
          context.emit('getTableData')
        }
      }
      // 分页相关：监听单页显示数量切换事件
      const handleSizeChange = (val: Number) => {
        timer = 'work'
        setTimeout(() => {
          timer = null
        }, 100)
        props.page.size = val
        context.emit('getTableData', true)
      }
      // 选择监听器
      const handleSelectionChange = (val: []) => {
        context.emit('selection-change', val)
      }

      // 获取行的 CSS 类名（用于高亮选中行）
      const getRowClassName = ({ row }: { row: any }) => {
        const isSelected =
          currentRow.value &&
          currentRow.value.id === row.id &&
          isContextMenuVisible.value

        // 强制触发响应式更新
        if (isSelected) {
          return 'context-menu-selected-row'
        }
        return ''
      }

      // 处理行右键菜单
      const handleRowContextMenu = (
        row: any,
        _column: any,
        event: MouseEvent
      ) => {
        if (!props.showContextMenu) return

        event.preventDefault()

        // 先重置状态，确保清除之前的高亮
        isContextMenuVisible.value = false
        currentRow.value = null

        // 使用 nextTick 确保状态更新后再设置新状态
        setTimeout(() => {
          currentRow.value = row
          isContextMenuVisible.value = true

          // 强制触发表格重新渲染
          if (table.value && table.value.doLayout) {
            table.value.doLayout()
          }
        }, 10)

        // 在隐藏元素上触发右键菜单
        if (contextMenuTrigger.value) {
          contextMenuTrigger.value.style.left = event.clientX + 'px'
          contextMenuTrigger.value.style.top = event.clientY + 'px'

          const contextMenuEvent = new MouseEvent('contextmenu', {
            bubbles: true,
            cancelable: true,
            clientX: event.clientX,
            clientY: event.clientY,
          })

          contextMenuTrigger.value.dispatchEvent(contextMenuEvent)

          setTimeout(() => {
            if (contextMenuTrigger.value) {
              contextMenuTrigger.value.style.left = '-9999px'
              contextMenuTrigger.value.style.top = '-9999px'
            }
          }, 100)
        }

        // 监听点击事件来隐藏右键菜单
        const hideContextMenu = () => {
          isContextMenuVisible.value = false
          currentRow.value = null
          document.removeEventListener('click', hideContextMenu)
          document.removeEventListener('contextmenu', hideContextMenu)
        }

        setTimeout(() => {
          document.addEventListener('click', hideContextMenu)
          document.addEventListener('contextmenu', hideContextMenu)
        }, 100)
      }

      // 处理右键菜单项点击
      const handleContextMenuClick = (item: any) => {
        const selectedRow = currentRow.value
        isContextMenuVisible.value = false
        currentRow.value = null

        // 发送事件给父组件
        context.emit('context-menu-click', {
          item,
          row: selectedRow,
        })
      }

      // 解决BUG：keep-alive组件使用时，表格浮层高度不对的问题
      onActivated(() => {
        table.value.doLayout()
      })
      return {
        table,
        contextmenu,
        contextMenuTrigger,
        currentRow,
        isContextMenuVisible,
        getRowClassName,
        handleRowContextMenu,
        handleContextMenuClick,
        handleCurrentChange,
        handleSizeChange,
        handleSelectionChange,
      }
    },
  })
</script>

<style lang="scss" scoped>
  .system-table-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    height: 100%;
    .system-table {
      flex: 1;
      height: 100%;
    }

    .system-page {
      margin-top: 20px;
    }
  }

  /* 表格 hover 效果样式 */
  :deep(.el-table__row:hover > td) {
    background-color: #e6f7ff !important;
  }

  /* 右键菜单选中行高亮样式 */
  :deep(.context-menu-selected-row) {
    background-color: #e6f7ff !important;
    border: 1px solid #1890ff;
  }

  :deep(.context-menu-selected-row:hover) {
    background-color: #e6f7ff !important;
  }
</style>
