import request from '@/utils/system/request'
import type {ResponseList,ResponseMessage} from '@/type/index.type'
import type { GroupListParams,GroupListRes,GroupParams,GroupDataListParams,GroupDataListRes,GroupDataParams } from '@/type/maintain.type'



//组合数据列表
export function getGroupList(data: GroupListParams):Promise<ResponseList<GroupListRes[]>> {
  return request({
    url: '/api/admin/system/group/list',
    method: 'get',
    params:data
  })
}
//新增组合数据
export function saveGroup(data: GroupParams):Promise<ResponseMessage> {
  return request({
    url: '/api/admin/system/group/save',
    method: 'post',
    params:data
  })
}
//编辑组合数据
export function updateGroup(data: GroupParams):Promise<ResponseMessage> {
  return request({
    url: '/api/admin/system/group/update',
    method: 'post',
    params:data
  })
}
//删除组合数据
export function deleteGroup(data: {id:number}):Promise<ResponseMessage> {
  return request({
    url: '/api/admin/system/group/delete',
    method: 'get',
    params:data
  })
}


//组合数据 数据列表  获取表格数据
export function groupDataList (pram:GroupDataListParams):Promise<ResponseList<GroupDataListRes[]>> {
  const data = {
    gid: pram.gid,
    keywords: pram.keywords,
    status: pram.status, // 1=开启 2=关闭
    page: pram.page,
    limit: pram.limit
  }
  return request({
    url: '/api/admin/system/group/data/list',
    method: 'GET',
    params: data
  })
}
//组合数据 数据列表  删除数据
export function groupDataDelete (pram:{id:number}):Promise<ResponseMessage> {
  return request({
    url: '/api/admin/system/group/data/delete',
    method: 'GET',
    params: pram
  })
}
//组合数据 新增
export function groupDataSave(data: GroupDataParams):Promise<ResponseMessage> {

  return request({
    url: '/api/admin/system/group/data/save',
    method: 'post',
    data:data,
  })
}
//组合数据 修改
export function groupDataUpate(data: GroupDataParams):Promise<ResponseMessage> {
  let obj={
    id:data.listDataId
  }
  return request({
    url: '/api/admin/system/group/data/update',
    method: 'post',
    data:data,
    params:obj
  })
}