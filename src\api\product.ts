import request from "@/utils/system/request"
import type {
  ProductHeaderRes,
  ProductListRes,
  ProductListParams,
  RuleListParams,
  RuleListRes,
  RuleParams
} from "@/type/product.type"
import type { ResponseList, ResponseMessage } from "@/type/index.type"
import {ProductStockChangeRequest} from "@/type/product.type";
// 获取商品表头
export function getTabsHearder(): Promise<ProductHeaderRes[]> {
  return request({
    url: "/api/admin/product/tabs/headers",
    method: "get",
  })
}

//获取商品列表
export function getProductList(
  data: ProductListParams
): Promise<ResponseList<ProductListRes[]>> {
  return request({
    url: "/api/admin/product/list",
    method: "get",
    params: data,
  })
}
//添加商品
export function saveProduct(data: object): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/save",
    method: "post",
    data: data,
  })
}
//商品详情
export function productInfo(data: any) {
  return request({
    url: "/api/admin/product/info/" + data,
    method: "get",
    data: data,
  })
}

//根据产品编码获取商品详情
export function getProductByCode(code: string) {
  return request({
    url: `/api/admin/product/attrValue/info/no/${code}`,
    method: "get",
  })
}

//根据产品SKU ID查找产品SKU
export function getProductSkuById(id: string) {
  return request({
    url: `/api/admin/product/attrValue/info/${id}`,
    method: "get",
  })
}

//商品规格列表
export function getProductRuleList(data: RuleListParams):Promise<ResponseList<RuleListRes[]>> {
  return request({
    url: "/api/admin/product/rule/list",
    method: "get",
    params: data,
  })
}
//新增商品规格
export function saveProductRule(data: RuleParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/rule/save",
    method: "post",
    data: data,
  })
}
//修改商品规格
export function updateProductRule(data: RuleParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/rule/update",
    method: "post",
    data: data,
  })
}
//删除商品规格
export function delProductRule(data: string): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/rule/delete/" + data,
    method: "get",
  })
}

//商品上架
export function putOnShell(data: number): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/putOnShell/" + data,
    method: "get",
  })
}
//商品下架
export function offShell(data: number): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/offShell/" + data,
    method: "get",
  })
}
//编辑商品
export function updateProduct(data: ProductListRes): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/update",
    method: "post",
    data: data,
  })
}
//删除商品
export function deleteProduct(data: number): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/delete/" + data,
    method: "get",
  })
}
//恢复商品
export function restoreProduct(data: number): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/restore/" + data,
    method: "get",
  })
}

// 获取商品评论
export function getProductReply(data: any) {
  return request({
    url: "/api/admin/product/reply/list",
    method: "get",
    params: data,
  })
}

// 获取商品评论详情信息
export function orderInfo(data: {id:number}) :Promise<ProductListRes>{
  return request({
    url: "/api/admin/order/infoById",
    method: "get",
    params: data,
  })
}

// 产品出入库记录列表
export function getStockList(
    data: ProductListParams
): Promise<ResponseList<any[]>> {
  return request({
    url: "/api/admin/product/stock/list",
    method: "get",
    params: data,
  })
}

// 获取产品sku列表
export function getSkuList(id: number) {
  return request({
    url: `/api/admin/product/skuList/${id}`,
    method: "get"
  })
}

// 产品出入库操作
export function changeStock(data: ProductStockChangeRequest) {
  return request({
    url: "/api/admin/product/stock/change",
    method: "post",
    data: data,
  })
}

//获取所有商品sku列表
export function getAttrValueList(
    data: any
): Promise<ResponseList<any[]>> {
  return request({
    url: "/api/admin/product/attrValue/list",
    method: "get",
    params: data,
  })
}

// 导出产品库存记录Excel
export function exportProductStock(data: {
  productId?: number
  stockOrderId?: number
  skuId?: number
  type?: number
  dateLimit?: string
}): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/product/stock/excelOutput",
    method: "post",
    data: data,
  })
}
