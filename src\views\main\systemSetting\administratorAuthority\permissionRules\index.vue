<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
      </div>
      <div class="layout-container-form-search">
        <el-button
          class="mr-[1rem]"
          type="success"
          :icon="Refresh"
          @click="handlePromission"
        >
          权限同步
        </el-button>
        <el-input v-model="query.name" placeholder="请输入名称"></el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        lazy
        v-loading="loading"
        :tree-props="{ children: 'childList', hasChildren: 'hasChildren' }"
        :header-cell-style="{ fontWeight: 'bold' }"
        border
      >
        <el-table-column prop="name" label="菜单名称" />
        <el-table-column prop="perms" label="权限标识" />
        <el-table-column prop="component" label="组件路径" min-width="200px" />
        <el-table-column label="图标">
          <template #default="scope">
            <i class="iconfont" :class="scope.row.icon"></i>
          </template>
        </el-table-column>
        <el-table-column prop="isShow" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.isShow ? '' : 'danger'">
              {{ scope.row.isShow ? '显示' : '隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="menuType" label="类型">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.menuType == 'M'">
              菜单
            </el-tag>
            <el-tag v-else-if="scope.row.menuType == 'C'">目录</el-tag>
            <el-tag type="warning" v-else>按钮</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="150"
        >
          <template #default="scope">
            <div class="operation-buttons">
              <el-button type="text" @click="handleEdit(scope.row)">
                修改
              </el-button>
              <el-popconfirm
                :title="$t('message.common.delTip')"
                @confirm="handleDel(scope.row.id)"
              >
                <template #reference>
                  <el-button type="text">删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>
<script lang="ts">
  export const getTree = (data: any) => {
    let result: any[] = []
    let obj: any = {}
    data.forEach((item: { id: string | number; pid: string | number }) => {
      obj[item.id] = Object.assign(item, obj[item.id] || {})
      if (item.pid) {
        let parent = obj[item.pid] || {}
        parent.child = parent.child || []
        parent.child.push(item)
        obj[item.pid] = parent
      } else {
        result.push(obj[item.id])
      }
    })
    return result
  }
</script>
<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getMenuList, deleteMenu } from '@/api/systemSetting'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import Table from '@/components/table/index.vue'
  import Layer from './layer.vue'
  import { Plus, Delete, Search, Refresh } from '@element-plus/icons-vue'
  import type { MenuListRes, MenuListParams } from '@/type/systemSetting.type'
  import { updateMenuApi } from '@/api/user'
  // 存储搜索用的数据
  const query = reactive({
    name: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<MenuListRes[]>([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页

  const getTableData = (init: Boolean) => {
    tableData.value = []
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: MenuListParams = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getMenuList(params)
      .then(res => {
        let data = res
        tableData.value = getTree(data)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (id: number) => {
    let params = id
    deleteMenu(params).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }
  // 编辑弹窗功能
  const handleEdit = (row: MenuListRes) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }

  const handlePromission = async () => {
    try {
      await updateMenuApi()
      ElMessage({
        type: 'success',
        message: '权限同步成功',
      })
    } catch (error) {
      console.log(error)
    }
  }

  getTableData(true)
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .operation-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
  }
</style>
