<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h2 class="dialog-title">选择赠品</h2>
        <p class="dialog-description">请选择要添加的赠品，可多选</p>
        <button class="close-button" @click="handleCancel">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="dialog-content">
        <!-- 产品搜索区域 -->
        <div class="search-section">
          <ProductSearch
            v-model:keywords="searchParams.keywords"
            v-model:cateId="searchParams.cateId"
            :options="categoryOptions"
            @search="handleSearch"
          />
        </div>

        <!-- 产品列表区域 -->
        <div class="table-section">
          <ProductTable
            ref="productTableRef"
            v-model:page="page"
            :loading="loading"
            :data="productList"
            @getTableData="handleGetTableData"
            @selection-change="handleSelectionChange"
          />
        </div>

        <!-- 已选择的产品提示 -->
        <div v-if="selectedProducts.length > 0" class="selected-info">
          <span class="selected-count">已选择 {{ selectedProducts.length }} 个产品</span>
          <el-button type="text" size="small" @click="clearSelection">清空选择</el-button>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="handleCancel">取消</button>
        <button 
          class="btn btn-primary" 
          @click="handleConfirm"
          :disabled="selectedProducts.length === 0"
        >
          确认选择 ({{ selectedProducts.length }})
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ProductSearch from '@/views/main/product/productSkuList/components/ProductSearch.vue'
import ProductTable from '@/views/main/product/productSkuList/components/ProductTable.vue'
import type { TreeCategroyRes } from '@/type/category.type'
import type { ProductListRes, ProductListParams } from '@/type/product.type'
import type { Page } from '@/components/table/type'
import { getAttrValueList } from '@/api/product'
import { treeCategroy } from '@/api/categoryApi'

// Props 定义
interface Props {
  visible: boolean
  selectedItems?: any[]
}

// Emits 定义
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', selectedItems: any[]): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedItems: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const productList = ref<ProductListRes[]>([])
const selectedProducts = ref<ProductListRes[]>([])
const categoryOptions = ref<TreeCategroyRes[]>([])
const productTableRef = ref()
// 添加标志位，防止自动选中时触发选择变化处理
const isAutoSelecting = ref(false)

// 搜索参数
const searchParams = reactive({
  keywords: '',
  cateId: ''
})

// 分页参数
const page = ref<Page>({
  index: 1,
  size: 10,
  total: 0
})

// 获取产品列表
const getProductListData = async (init = false) => {
  if (init) {
    page.value.index = 1
  }
  
  loading.value = true
  try {
    const params: any = {
      page: page.value.index,
      limit: page.value.size,
      keywords: searchParams.keywords || undefined,
      cateId: searchParams.cateId || undefined
    }

    const response = await getAttrValueList(params)
    if (response.list) {
      productList.value = response.list || []
      page.value.total = response.total || 0



      // 如果有缓存的选中数据，自动选中对应的行（参考订单出库页面）
      if (selectedProducts.value.length > 0) {
        nextTick(() => {
          setTimeout(() => {
            autoSelectRowsFromCache()
          }, 100) // 参考订单出库页面的延迟时间
        })
      }
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类树数据
const getCategoryData = async () => {
  try {
    const response = await treeCategroy({
      type: 1, // 产品分类
      status: 1 // 已生效
    })
    categoryOptions.value = response || []
  } catch (error) {
    console.error('获取分类数据失败:', error)
  }
}

// 从缓存中自动选中行的函数（简化版本，依赖reserve-selection）
const autoSelectRowsFromCache = () => {
  // 尝试多种方式访问表格实例
  let elTable = null

  if (productTableRef.value?.table?.table) {
    elTable = productTableRef.value.table.table
  } else if (productTableRef.value?.table) {
    elTable = productTableRef.value.table
  } else if (productTableRef.value?.$refs?.table) {
    elTable = productTableRef.value.$refs.table
  }

  if (!elTable) {
    return
  }

  // 设置自动选中标志，防止触发选择变化处理
  isAutoSelecting.value = true

  // 由于使用了reserve-selection，我们只需要确保当前页面的选中状态正确
  // 不需要手动清除和重新选择，reserve-selection会自动处理
  selectedProducts.value.forEach(cachedItem => {
    const matchedRow = productList.value.find(row =>
      String(cachedItem.id).trim() === String(row.id).trim()
    )

    if (matchedRow && elTable.toggleRowSelection) {
      try {
        // 只选中当前页面存在的行
        elTable.toggleRowSelection(matchedRow, true)
      } catch (error) {
        console.warn('选中行失败:', error)
      }
    }
  })

  // 重置自动选中标志
  setTimeout(() => {
    isAutoSelecting.value = false
  }, 50)


}

// 注意：由于使用了reserve-selection，Element Plus会自动管理全选状态
// 我们不需要手动更新全选状态

// 恢复选择状态（保持向后兼容，某些地方可能还在使用这个函数名）
// const restoreSelection = autoSelectRowsFromCache

// 处理搜索
const handleSearch = () => {
  getProductListData(true)
}

// 处理分页变化（参考订单出库页面）
const handleGetTableData = (init?: boolean) => {
  getProductListData(init)
}

// 去重函数（参考订单出库页面）
const removeDuplicates = (array: ProductListRes[]) => {
  const seen = new Set()
  return array.filter(item => {
    const key = String(item.id).trim()
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

// 处理选择变化（简化版本，配合reserve-selection）
const handleSelectionChange = (selection: ProductListRes[]) => {
  // 如果是自动选中过程，忽略这次选择变化
  if (isAutoSelecting.value) {
    return
  }

  // 由于使用了reserve-selection，我们需要更智能地处理选择变化
  // reserve-selection会自动保持跨页面的选中状态，我们只需要同步全局状态

  // 获取当前页面的产品ID列表
  const currentPageIds = productList.value.map(item => item.id)

  // 移除当前页面的数据
  selectedProducts.value = selectedProducts.value.filter(
    item => !currentPageIds.includes(item.id)
  )

  // 添加当前页面新选中的数据
  selectedProducts.value.push(...selection)

  // 去重
  selectedProducts.value = removeDuplicates(selectedProducts.value)
}

// 清空选择
const clearSelection = () => {
  selectedProducts.value = []

  // 使用 nextTick 确保状态更新后再清空表格选择
  nextTick(() => {
    // 设置自动选中标志，防止触发选择变化处理
    isAutoSelecting.value = true

    // 尝试多种方式访问表格实例
    let elTable = null

    if (productTableRef.value?.table?.table) {
      elTable = productTableRef.value.table.table
    } else if (productTableRef.value?.table) {
      elTable = productTableRef.value.table
    } else if (productTableRef.value?.$refs?.table) {
      elTable = productTableRef.value.$refs.table
    }

    if (elTable && elTable.clearSelection) {
      try {
        elTable.clearSelection()
      } catch (error) {
        console.warn('清空选择失败:', error)
      }
    }

    // 重置自动选中标志
    setTimeout(() => {
      isAutoSelecting.value = false
    }, 50)
  })
}

// 处理确认
const handleConfirm = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请至少选择一个产品')
    return
  }

  // 转换数据格式，与 outboundEntityList 保持一致
  const formattedItems = selectedProducts.value.map(product => {
    // 确保数量字段的数据类型和格式正确
    const unitNames = (product as any).unitNames || ['件']
    const unitNums = (product as any).unitNums || new Array(unitNames.length).fill(0)
    const changeUnitNums = (product as any).changeUnitNums || new Array(unitNames.length).fill(0)

    const formattedItem = {
      id: product.id,
      productName: product.productName,
      image: product.image,
      suk: (product as any).suk || '', // 直接使用 suk 字段
      unitGroupStr: (product as any).unitGroupStr || '', // 直接使用 unitGroupStr 字段
      unitNames: unitNames, // 确保单位名称数组
      unitNums: unitNums, // 确保数量数组长度与单位名称匹配
      changeUnitNums: changeUnitNums, // 确保变更数量数组长度与单位名称匹配
      unitPrice: 0, // 赠品单价为0
      price: 0, // 赠品金额为0
      attrValueNo: (product as any).attrValueNo || '', // 直接使用 attrValueNo 字段
      // 添加赠品标识
      isGift: true
    }

    return formattedItem
  })

  emit('confirm', formattedItems)
  emit('update:visible', false)
}

// 处理取消
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  handleCancel()
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹窗打开时初始化数据
    getCategoryData()

    // 恢复之前选中的项目 - 需要转换数据格式并保留用户填写的数量信息
    if (props.selectedItems && props.selectedItems.length > 0) {
      // 将已选中的赠品数据转换为产品数据格式，以便正确比较
      // 重要：保留用户已填写的数量信息
      selectedProducts.value = props.selectedItems.map(gift => ({
        id: gift.id,
        productName: gift.productName,
        image: gift.image,
        suk: gift.suk,
        unitGroupStr: gift.unitGroupStr,
        unitNames: gift.unitNames || ['件'],
        // 保留用户已填写的数量信息
        unitNums: gift.unitNums || [0],
        changeUnitNums: gift.changeUnitNums || [0],
        attrValueNo: gift.attrValueNo,
        unitPrice: gift.unitPrice || 0,
        price: gift.price || 0,
        // 保留原始的赠品标识，以便后续处理
        isGift: gift.isGift || true
      }))

    } else {
      selectedProducts.value = []
    }

    // 获取产品列表数据
    getProductListData(true)
  } else {
    // 弹窗关闭时重置状态（但不清空selectedProducts，保持选中状态）
    searchParams.keywords = ''
    searchParams.cateId = ''
    productList.value = []
    page.value.index = 1
    // 注意：不清空selectedProducts.value，保持用户的选择状态
  }
})

// 监听分页变化 - 移除这个监听器，因为分页逻辑已经在 handleGetTableData 中处理
// watch(() => page.value, (newPage) => {
//   if (props.visible) {
//     getProductListData()
//   }
// }, { deep: true })
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.dialog-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.dialog-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.dialog-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.close-button:hover {
  color: #374151;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-section {
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.table-section {
  flex: 1;
  min-height: 400px;
}

.selected-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  margin-top: 16px;
}

.selected-count {
  font-size: 14px;
  color: #1e40af;
  font-weight: 500;
}

.dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  min-width: 80px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-overlay {
    padding: 10px;
  }
  
  .dialog-container {
    max-height: 95vh;
    max-width: 100%;
  }
  
  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .search-section {
    padding: 12px;
  }
  
  .table-section {
    min-height: 300px;
  }
}

/* 滚动条样式 */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>