<template>
    <el-dialog v-model="layer.show" title="用户信息" width="50%" append-to-body draggable>
        <el-descriptions title="" border direction="vertical" :column="5">
            <el-descriptions-item align="center" label="用户id">
                <el-tag>{{ data.uid }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户账号">
                <el-tag>{{ data.account }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="真实姓名">
                <el-tag>{{ data.realName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="生日">
                <el-tag>{{ data.birthday }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="身份证号码">
                <el-tag>{{ data.birthday }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户备注">
                <el-tag>{{ data.mark }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户昵称">
                <el-tag>{{ data.nickName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户头像">
                <imagePreview :imgurl="data.avatar"></imagePreview>
                <!-- <el-image style="width: 30px; height: 30px" :src="picurl + data.avatar"
                    :preview-src-list="[(picurl + data.avatar)]" :hide-on-click-modal="true" /> -->
            </el-descriptions-item>
            <el-descriptions-item align="center" label="手机号">
                <el-tag>{{ data.phone }}</el-tag>
            </el-descriptions-item>
            <!-- <el-descriptions-item align="center" label="微信开放id">
                <el-tag>{{ data.wxOpenId }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="性别">
                <el-tag>{{ data.sex == 1 ? '男' : '女' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="国家">
                <el-tag>{{ data.country }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="城市">
                <el-tag>{{ data.city }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="添加ip">
                <el-tag>{{ data.addIp }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="最后一次登录ip">
                <el-tag>{{ data.lastIp }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="佣金金额">
                <el-tag>{{ data.brokeragePrice }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="余额">
                <el-tag>{{ data.integral }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="种树积分">
                <el-tag>{{ data.plantTreeIntegral }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="仓单">
                <el-tag>{{ data.cang }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="能量">
                <el-tag>{{ data.energy }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="经验">
                <el-tag>{{ data.experience }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="连续签到天数">
                <el-tag>{{ data.signNum }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="状态">
                <el-tag v-if="data.status == 1">正常</el-tag>
                <el-tag v-else-if="data.status == 0" type="danger">禁止</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="等级">
                <el-tag>{{ data.level }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="推广人id">
                <el-tag>{{ data.spreadUid }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="推广人关联时间">
                <el-tag>{{ data.spreadTime }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户类型">
                <el-tag>{{ data.userType }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="是否为推广员">
                <el-tag>{{ data.userType ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户购买次数">
                <el-tag>{{ data.payCount }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="下级人数">
                <el-tag>{{ data.spreadCount }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="详细地址">
                <el-tag>{{ data.addres }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="管理员编号">
                <el-tag>{{ data.adminid }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户登录类型">
                <el-tag>{{ data.loginType }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="更新时间">
                <el-tag>{{ data.updateTime }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="创建时间">
                <el-tag>{{ data.createTime }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="更新时间">
                <el-tag>{{ data.updateTime }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="最后一次登录时间">
                <el-tag>{{ data.lastLoginTime }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="用户推广等级">
                <el-tag>{{ data.path }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="是否关注公众号">
                <el-tag>{{ data.subscribe ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="成为分销员时间">
                <el-tag>{{ data.promoterTime }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="邀请码">
                <el-tag>{{ data.inviteCode }}</el-tag>
            </el-descriptions-item>

            <el-descriptions-item align="center" label="支付宝收款码">
                <el-image style="width: 30px; height: 30px" :src="picurl + data.zhifubaoPaymentPic"
                    :preview-src-list="[(picurl + data.zhifubaoPaymentPic)]" :hide-on-click-modal="true" />
            </el-descriptions-item>
            <el-descriptions-item align="center" label="微信收款码">
                <el-image style="width: 30px; height: 30px" :src="picurl + data.weixinPaymentPic"
                    :preview-src-list="[(picurl + data.weixinPaymentPic)]" :hide-on-click-modal="true" />
            </el-descriptions-item>
            <el-descriptions-item align="center" label="管理的项目编码">
                <el-tag>{{ data.managerProjectNo }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="银行名称">
                <el-tag>{{ data.bankName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="银行卡号">
                <el-tag>{{ data.bankCardNo }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="开户行名称">
                <el-tag>{{ data.bankOpenName }}</el-tag>
            </el-descriptions-item> -->
        </el-descriptions>
    </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, ref, reactive } from "vue"
import { getUserInfo } from '@/api/userManage'
import imagePreview from '../imagePreview/index.vue'
import { getPicUrl } from '@/utils/index'
import type {UserListRes} from '@/type/userManage.type'
const props = defineProps({
    layer: {
        type: Object,
        default: () => {
            return {
                show: false, //是否显示
                id: null  //用户id
            }
        }
    }
})
const data = ref<UserListRes>({
})

const picurl = getPicUrl()
getUserInfo({ id: props.layer.id }).then(res => {
    data.value = res
    // if (data.value.tagId!='') {
    //     getTagInfo({ id: data.value.tagId }).then(res => {

    //     })
    // }


})
</script>

<style lang="scss" scoped></style>
