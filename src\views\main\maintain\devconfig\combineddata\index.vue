<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
      </div>
      <div class="layout-container-form-search">
        <el-input v-model="query.keywords" placeholder="请输入名称"></el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column prop="name" label="名称" align="center" />
        <el-table-column prop="info" label="描述" align="center" />
        <el-table-column prop="createTime" label="更新时间" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-button @click="ShowFormList(scope.row)" type="primary">
              数据列表
            </el-button>
            <el-button @click="handleEdit(scope.row)">
              {{ $t('message.common.update') }}
            </el-button>
            <el-popconfirm
              :title="$t('message.common.delTip')"
              @confirm="handleDel([scope.row])"
            >
              <template #reference>
                <el-button type="danger">
                  {{ $t('message.common.del') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
      <FormLayer
        :layer="formlayer"
        @getTableData="getTableData"
        v-if="formlayer.show"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getGroupList, deleteGroup, updateGroup } from '@/api/maintain'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import Table from '@/components/table/index.vue'
  import Layer from './layer.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import FormLayer from './fornlayer.vue'
  import type { GroupListRes } from '@/type/maintain.type'

  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  const formlayer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<GroupListRes[]>([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getGroupList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: object[]) => {
    let params = {
      id: data
        .map((e: any) => {
          return e.id
        })
        .join(','),
    }
    deleteGroup({ id: Number(params.id) }).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }
  // 数据列表页面
  const ShowFormList = (row: any) => {
    formlayer.title = '数据列表'
    formlayer.row = row
    formlayer.show = true
    formlayer.showButton = false
  }
  // 编辑弹窗功能
  const handleEdit = (row: any) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }
  getTableData(true)
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
