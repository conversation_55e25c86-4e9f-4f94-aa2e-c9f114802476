<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h2 class="dialog-title">{{ title }}</h2>
        <p class="dialog-description">请填写原料信息</p>
        <button class="close-button" @click="handleCancel">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div class="dialog-content">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="原料分类" prop="cateId">
            <el-cascader
              v-model="form.cateId"
              :options="categoryOptions"
              :props="categoryProps"
              clearable
              placeholder="请选择原料分类"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="原料名称" prop="rawMaterialName">
            <el-input
              v-model="form.rawMaterialName"
              placeholder="请输入原料名称"
            />
          </el-form-item>

          <el-form-item label="单位组" prop="unitId">
            <div class="unit-group-input-wrapper">
              <el-select
                v-model="form.unitId"
                placeholder="请选择单位组"
                class="unit-group-select"
                @change="handleUnitGroupChange"
              >
                <el-option
                  v-for="item in unitGroupOptions"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                />

                <!-- 单位组为空时的提示链接 -->
                <template #empty>
                  <div class="empty-unit-group-tip-dropdown">
                    <span class="tip-text">暂无可用单位组，</span>
                    <a href="#" @click.prevent="navigateToUnitManagement" class="tip-link">
                      <svg class="tip-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                      点击前往添加
                    </a>
                  </div>
                </template>
              </el-select>

              <!-- 刷新按钮 -->
              <el-button
                type="primary"
                size="default"
                class="refresh-button"
                :loading="refreshLoading"
                @click="handleRefreshUnitGroups"
              >
                刷新
              </el-button>
            </div>
          </el-form-item>

          <!-- 动态单位数量输入 -->
          <template v-if="selectedUnitGroup && selectedUnitGroup.unitNameList">
            <div class="unit-group-section">
              <div class="unit-group-title">
                <span>{{ selectedUnitGroup.groupName }} - 各单位数量</span>
                <small class="unit-group-desc">
                  <template v-if="selectedUnitGroup.scaleList && selectedUnitGroup.unitNameList">
                    换算比例：
                    <span v-for="(unitName, index) in selectedUnitGroup.unitNameList" :key="index">
                      <span :class="{ 'base-unit': unitName === selectedUnitGroup.base }">
                        {{ unitName }}
                        <template v-if="selectedUnitGroup.scaleList[index] !== undefined">
                          ({{ selectedUnitGroup.scaleList[index] }})
                        </template>
                        <template v-if="unitName === selectedUnitGroup.base">
                          [基础单位]
                        </template>
                      </span>
                      <span v-if="index < selectedUnitGroup.unitNameList.length - 1"> : </span>
                    </span>
                  </template>
                  <template v-else>
                    比例：{{ selectedUnitGroup.unitNames }} ({{ selectedUnitGroup.scales }})
                  </template>
                </small>
              </div>
              <div class="unit-inputs-grid">
                <el-form-item
                  v-for="(unitName, index) in selectedUnitGroup.unitNameList"
                  :key="`unit_${index}`"
                  :prop="`unitNums.${index}`"
                  class="unit-input-item"
                >
                  <template #label>
                    <span :class="{ 'base-unit-label': unitName === selectedUnitGroup.base }">
                      {{ unitName }}
                      <template v-if="unitName === selectedUnitGroup.base">
                        <el-tag size="small" type="primary" class="base-unit-tag">基础</el-tag>
                      </template>
                    </span>
                  </template>
                  <el-input-number
                    v-model="form.unitNums[index]"
                    :min="0"
                    :precision="0"
                    controls-position="right"
                    style="width: 100%"
                    :placeholder="`请输入${unitName}数量`"
                  />
                </el-form-item>
              </div>
            </div>

            <!-- 警戒库存设置区域 -->
            <div class="unit-group-section alert-stock-section">
              <div class="unit-group-title alert-stock-title">
                <span class="title-with-icon">
                  <svg class="alert-warning-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                    <path d="M12 9v4"/>
                    <path d="m12 17 .01 0"/>
                  </svg>
                  {{ selectedUnitGroup.groupName }} - 警戒库存设置
                </span>
                <small class="unit-group-desc alert-stock-desc">
                  <svg class="desc-info-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="m9 12 2 2 4-4"/>
                  </svg>
                  设置各单位的警戒库存数量，当库存低于此值时将显示警戒提示
                </small>
              </div>

              <!-- 单行组合警戒值输入 -->
              <div class="alert-combined-section">
                <div class="alert-units-inline">
                  <span class="text-[.9rem]">警戒值：</span>
                  <template v-for="(unitName, index) in selectedUnitGroup.unitNameList" :key="`alert_${index}`">
                    <el-form-item
                      :prop="`storeStockNums.${index}`"
                      class="alert-unit-form-item"
                    >
                      <div class="alert-unit-group">
                        <el-input-number
                          v-model="form.storeStockNums[index]"
                          :min="0"
                          :precision="0"
                          controls-position="right"
                          size="small"
                          class="alert-inline-input"
                          :placeholder="0"
                        />
                        <span class="alert-unit-label" :class="{ 'base-unit-text': unitName === selectedUnitGroup.base }">
                          {{ unitName }}
                          <template v-if="unitName === selectedUnitGroup.base">
                            <el-tag size="small" type="primary" class="base-unit-tag-inline">基础</el-tag>
                          </template>
                        </span>
                      </div>
                    </el-form-item>
                  </template>
                </div>
              </div>
            </div>
          </template>


        </el-form>
      </div>

      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import { useRouter } from 'vue-router'
  import type { MaterialListRes } from '@/type/material.type'
  import type { TreeCategroyRes } from '@/type/category.type'
  import type { unitGroupSaveParams } from '@/type/units.type'
  import { getUnitGroupList } from '@/api/units'

  // 定义 props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    materialData: {
      type: Object as () => MaterialListRes | null,
      default: null,
    },
    categoryOptions: {
      type: Array as () => TreeCategroyRes[],
      default: () => [],
    },
  })

  // 定义 emits
  const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

  // 路由实例
  const router = useRouter()

  // 表单数据
  const form = ref({
    id: 0,
    cateId: '' as string | number, // 支持字符串和数字类型，以兼容级联选择器
    rawMaterialName: '',
    rawMaterialNum: 0,
    unitId: undefined as number | undefined, // 单位组ID（重命名自unitId）
    unitNums: [] as number[],
    storeStockNums: [] as number[], // 警戒库存数量数组
  })

  // 表单引用
  const formRef = ref()

  // 加载状态
  const loading = ref(false)

  // 单位组选项
  const unitGroupOptions = ref<unitGroupSaveParams[]>([])

  // 选中的单位组
  const selectedUnitGroup = ref<unitGroupSaveParams | null>(null)

  // 刷新按钮加载状态
  const refreshLoading = ref(false)

  // 分类选择器配置
  const categoryProps = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }

  // 计算属性
  const isEdit = computed(() => {
    return props.materialData && props.materialData.id > 0
  })

  const title = computed(() => {
    return isEdit.value ? '编辑原料' : '新增原料'
  })

  // 获取单位组列表
  const fetchUnitGroups = async () => {
    try {
      const res = await getUnitGroupList({ page: 1, limit: 10000, type: 2 })
      unitGroupOptions.value = res.list
    } catch (error) {
      console.error('获取单位组列表失败:', error)
    }
  }



  // 处理单位组变化
  const handleUnitGroupChange = (unitId: number | undefined) => {
    if (unitId) {
      const foundUnitGroup = unitGroupOptions.value.find(item => item.id === unitId)
      if (foundUnitGroup) {
        // 处理单位组数据，确保包含 unitNameList 和 scaleList
        if (!foundUnitGroup.unitNameList && foundUnitGroup.unitNames) {
          foundUnitGroup.unitNameList = foundUnitGroup.unitNames.split(',')
        }
        if (!foundUnitGroup.scaleList && foundUnitGroup.scales) {
          foundUnitGroup.scaleList = foundUnitGroup.scales.split(',').map(Number)
        }

        selectedUnitGroup.value = foundUnitGroup

        if (foundUnitGroup.unitNameList) {
          // 保留现有的 unitNums 数据，只在必要时调整数组长度
          const currentUnitNums = [...form.value.unitNums]
          const currentStoreStockNums = [...form.value.storeStockNums]
          const requiredLength = foundUnitGroup.unitNameList.length

          if (currentUnitNums.length !== requiredLength) {
            // 调整数组长度，保留现有数据
            form.value.unitNums = new Array(requiredLength).fill(0)
            for (let i = 0; i < Math.min(currentUnitNums.length, requiredLength); i++) {
              form.value.unitNums[i] = currentUnitNums[i] || 0
            }
          }

          if (currentStoreStockNums.length !== requiredLength) {
            // 调整警戒库存数组长度，保留现有数据
            form.value.storeStockNums = new Array(requiredLength).fill(0)
            for (let i = 0; i < Math.min(currentStoreStockNums.length, requiredLength); i++) {
              form.value.storeStockNums[i] = currentStoreStockNums[i] || 0
            }
          }
        }
      } else {
        selectedUnitGroup.value = null
      }
    } else {
      selectedUnitGroup.value = null
    }
  }

  // 表单验证规则
  const rules = computed(() => {
    return {
      cateId: [{ required: true, message: '请选择原料分类', trigger: 'change' }],
      rawMaterialName: [
        { required: true, message: '请输入原料名称', trigger: 'blur' },
      ],
      unitId: [{ required: true, message: '请选择单位组', trigger: 'change' }],
      // 动态验证各单位数量
      ...generateUnitNumsRules(),
    }
  })

  // 生成单位数量验证规则
  const generateUnitNumsRules = () => {
    const rules: any = {}
    if (selectedUnitGroup.value && selectedUnitGroup.value.unitNameList) {
      selectedUnitGroup.value.unitNameList.forEach((unitName, index) => {
        // 单位数量验证规则
        rules[`unitNums.${index}`] = [
          { required: true, message: `请输入${unitName}数量`, trigger: 'blur' },
          { type: 'number', min: 0, message: `${unitName}数量不能小于0`, trigger: 'blur' },
        ]
        // 警戒库存验证规则
        rules[`storeStockNums.${index}`] = [
          { required: true, message: `请输入${unitName}警戒库存`, trigger: 'blur' },
          { type: 'number', min: 0, message: `${unitName}警戒库存不能小于0`, trigger: 'blur' },
        ]
      })
    }
    return rules
  }

  // 监听弹窗显示状态，重置或填充表单
  watch(
    () => props.visible,
    async newVal => {
      if (newVal) {
        if (props.materialData && props.materialData.id > 0) {
          // 编辑模式，填充数据
          form.value = {
            id: props.materialData.id,
            // 将字符串类型的 cateId 转换为数字类型，以匹配级联选择器的 value 配置
            cateId: props.materialData.cateId
              ? Number(props.materialData.cateId)
              : '',
            rawMaterialName: props.materialData.rawMaterialName,
            rawMaterialNum: props.materialData.rawMaterialNum,
            unitId: props.materialData.unitId, // 使用unitId填充unitId字段
            unitNums: props.materialData.unitNums || [],
            storeStockNums: props.materialData.storeStockNums || [], // 填充警戒库存数据
          }

          // 如果有单位组ID，在下拉框选项中找到对应的单位组并选中
          if (props.materialData.unitId) {
            // 等待单位组列表加载完成后再设置选中项
            await nextTick()
            handleUnitGroupChange(props.materialData.unitId)
          }
        } else {
          // 新增模式，重置表单
          form.value = {
            id: 0,
            cateId: '',
            rawMaterialName: '',
            rawMaterialNum: 0,
            unitId: undefined,
            unitNums: [],
            storeStockNums: [], // 重置警戒库存数据
          }
          selectedUnitGroup.value = null
        }

        if (formRef.value) {
          formRef.value.clearValidate()
        }
      }
    }
  )

  // 处理取消
  const handleCancel = () => {
    emit('update:visible', false)
    emit('cancel')
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()

      loading.value = true

      const data: any = {
        // 只包含需要的字段
        rawMaterialName: form.value.rawMaterialName,
        unitNums: form.value.unitNums,
        unitId: form.value.unitId,
        storeStockNums: form.value.storeStockNums, // 包含警戒库存数据
        // 将数字类型的 cateId 转换为字符串类型，以匹配 API 要求
        cateId: form.value.cateId ? String(form.value.cateId) : '',
      }

      // 如果是编辑模式，添加id字段
      if (isEdit.value) {
        data.id = form.value.id
      }



      emit('confirm', data)
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 处理遮罩层点击
  const handleOverlayClick = () => {
    handleCancel()
  }

  // 跳转到原料单位组管理页面
  const navigateToUnitManagement = () => {
    // 在新标签页打开，避免丢失当前表单内容
    const routeUrl = router.resolve('/unit/marterial')
    window.open(routeUrl.href, '_blank')
  }

  // 刷新单位组列表
  const handleRefreshUnitGroups = async () => {
    refreshLoading.value = true
    try {
      await fetchUnitGroups()
      ElMessage.success('单位组列表已刷新')
    } catch (error) {
      ElMessage.error('刷新单位组列表失败')
    } finally {
      refreshLoading.value = false
    }
  }

  // 组件挂载时获取单位组数据
  onMounted(() => {
    fetchUnitGroups()
  })
</script>

<style scoped>
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .dialog-container {
    background: white;
    border-radius: 12px;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .dialog-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
  }

  .dialog-title {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px 0;
  }

  .dialog-description {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s;
  }

  .close-button:hover {
    color: #374151;
  }

  .dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }

  .dialog-footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .dialog-footer .el-button {
    padding: 10px 20px;
    font-size: 14px;
  }

  /* 单位组相关样式 */
  .unit-group-section {
    margin: var(--system-spacing-lg, 16px) 0;
    padding: var(--system-spacing-lg, 16px);
    background-color: var(--system-page-background);
    border-radius: var(--system-card-radius, 8px);
    border: 1px solid var(--system-page-border-color);
    box-shadow: var(--system-card-shadow);
    transition: all 0.3s ease;
  }

  .unit-group-title {
    margin-bottom: var(--system-spacing-md, 12px);
    padding-bottom: var(--system-spacing-sm, 8px);
    border-bottom: 1px solid var(--system-page-border-color);
  }

  .unit-group-title span {
    font-weight: 600;
    color: var(--system-page-color);
    font-size: 14px;
  }

  .unit-group-desc {
    display: block;
    margin-top: var(--system-spacing-xs, 4px);
    color: var(--system-page-tip-color);
    font-size: 12px;
    line-height: 1.4;
  }

  .unit-inputs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--system-spacing-lg, 16px);
    margin-top: var(--system-spacing-md, 12px);
  }

  .unit-input-item {
    margin-bottom: 0;
  }

  .unit-input-item .el-form-item__label {
    font-weight: 500;
    color: var(--system-page-secondary-color);
  }

  /* 警戒库存设置增强样式 */
  .alert-stock-section {
    border-left: 4px solid var(--system-page-warning-color);
    background: linear-gradient(135deg,
      var(--system-page-background) 0%,
      var(--el-color-warning-light-9, #fdf6ec) 100%);
    position: relative;
  }

  .alert-stock-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-top: 20px solid var(--el-color-warning-light-7, #f7d794);
    opacity: 0.6;
  }

  .alert-stock-title .title-with-icon {
    display: flex;
    align-items: center;
    gap: var(--system-spacing-sm, 8px);
    color: var(--el-color-warning-dark-2, #b88230);
    font-weight: 600;
  }

  .alert-warning-icon {
    color: var(--system-page-warning-color);
    flex-shrink: 0;
  }

  .alert-stock-desc {
    display: flex;
    align-items: flex-start;
    gap: var(--system-spacing-xs, 4px);
    color: var(--el-color-warning-dark-2, #b88230);
    background: var(--el-color-warning-light-9, #fdf6ec);
    padding: var(--system-spacing-sm, 8px) var(--system-spacing-md, 12px);
    border-radius: var(--system-spacing-xs, 4px);
    border: 1px solid var(--el-color-warning-light-6, #eebe77);
    margin-top: var(--system-spacing-sm, 8px);
  }

  .desc-info-icon {
    color: var(--system-page-warning-color);
    flex-shrink: 0;
    margin-top: 1px;
  }

  .alert-input-label {
    color: var(--el-color-warning-dark-2, #b88230);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--system-spacing-xs, 4px);
    white-space: nowrap;
  }

  .alert-input-number {
    width: 100%;
  }

  .alert-input-item :deep(.el-input-number) {
    width: 100%;
  }

  .alert-input-item :deep(.el-input__wrapper) {
    border: 1px solid var(--el-color-warning-light-5, #f0c78a);
    border-radius: var(--system-spacing-xs, 4px);
    transition: all 0.3s ease;
    background: var(--system-page-background);
  }

  .alert-input-item :deep(.el-input__wrapper:hover) {
    border-color: var(--system-page-warning-color);
    box-shadow: 0 2px 8px rgba(230, 162, 60, 0.15);
  }

  .alert-input-item :deep(.el-input__wrapper.is-focus) {
    border-color: var(--system-page-warning-color);
    box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
  }

  .alert-input-item :deep(.el-input__inner::placeholder) {
    color: var(--el-color-warning-light-3, #d4a574);
    font-style: italic;
  }

  .alert-input-item :deep(.el-input-number__increase),
  .alert-input-item :deep(.el-input-number__decrease) {
    border-color: var(--el-color-warning-light-5, #f0c78a);
    color: var(--system-page-warning-color);
  }

  .alert-input-item :deep(.el-input-number__increase:hover),
  .alert-input-item :deep(.el-input-number__decrease:hover) {
    background: var(--el-color-warning-light-8, #faecd8);
    color: var(--el-color-warning-dark-2, #b88230);
  }

  /* 基础单位样式 */
  .base-unit {
    font-weight: 600;
    color: #1d4ed8;
  }

  .base-unit-label {
    font-weight: 600;
    color: #1d4ed8;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .base-unit-tag {
    font-size: 10px;
    height: 16px;
    line-height: 14px;
    padding: 0 4px;
    border-radius: 2px;
  }

  /* 单位组输入包装器样式 */
  .unit-group-input-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
  }

  .unit-group-select {
    flex: 1;
    min-width: 0; /* 确保flex子项可以收缩 */
  }

  .refresh-button {
    flex-shrink: 0;
    min-width: 60px;
    height: 32px;
    font-size: 14px;
    padding: 0 16px;
    transition: all 0.2s ease;
  }

  .refresh-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }

  .refresh-button:active {
    transform: translateY(0);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .unit-group-input-wrapper {
      gap: var(--system-spacing-sm, 8px);
    }

    .refresh-button {
      min-width: 50px;
      padding: 0 var(--system-spacing-md, 12px);
      font-size: 13px;
    }

    /* 警戒值设置响应式优化 */
    .alert-stock-section {
      margin: var(--system-spacing-md, 12px) 0;
      padding: var(--system-spacing-md, 12px);
    }

    .alert-stock-title .title-with-icon {
      gap: var(--system-spacing-xs, 4px);
      font-size: 13px;
    }

    .alert-warning-icon {
      width: 14px;
      height: 14px;
    }

    .alert-stock-desc {
      font-size: 11px;
      padding: var(--system-spacing-xs, 4px) var(--system-spacing-sm, 8px);
      gap: var(--system-spacing-xs, 4px);
    }

    .desc-info-icon {
      width: 12px;
      height: 12px;
    }

    .alert-inputs-grid {
      grid-template-columns: 1fr;
      gap: var(--system-spacing-md, 12px);
    }
  }

  /* 暗色主题适配 */
  [data-theme="dark"] .alert-stock-section {
    background: linear-gradient(135deg,
      var(--system-page-background) 0%,
      rgba(245, 158, 11, 0.05) 100%);
    border-left-color: var(--system-page-warning-color);
  }

  [data-theme="dark"] .alert-stock-section::before {
    border-top-color: rgba(245, 158, 11, 0.3);
  }

  [data-theme="dark"] .alert-stock-desc {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
    color: #d4a574;
  }

  [data-theme="dark"] .alert-input-item :deep(.el-input__wrapper) {
    background: var(--system-page-background);
    border-color: rgba(245, 158, 11, 0.4);
  }

  [data-theme="dark"] .alert-input-item :deep(.el-input__wrapper:hover) {
    border-color: var(--system-page-warning-color);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
  }

  [data-theme="dark"] .alert-input-item :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
  }

  /* 下拉框内空状态提示链接样式 */
  .empty-unit-group-tip-dropdown {
    padding: 16px 20px;
    text-align: center;
    background-color: var(--el-color-warning-light-9, #fdf6ec);
    border-radius: 6px;
    margin: 8px;
    font-size: 13px;
    line-height: 1.4;
    border: 1px solid var(--el-color-warning-light-5, #f0c78a);
  }

  .tip-text {
    color: var(--el-color-warning-dark-2, #b88230);
    font-weight: 500;
  }

  .tip-link {
    color: var(--system-primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
    margin-left: 2px;
  }

  .tip-link:hover {
    color: var(--el-color-primary-dark-2, #337ecc);
    background-color: var(--el-color-primary-light-9, #ecf5ff);
    text-decoration: underline;
  }

  .tip-link:active {
    transform: translateY(1px);
  }

  .tip-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2.5;
    transition: transform 0.2s ease;
    color: inherit;
  }

  .tip-link:hover .tip-icon {
    transform: scale(1.1);
  }

  /* 单行组合警戒值输入样式 */
  .alert-combined-section {
    margin-bottom: var(--system-spacing-lg, 16px);
  }

  .alert-combined-label {
    margin-bottom: var(--system-spacing-sm, 8px);
  }

  .alert-combined-label span {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-regular);
  }

  .alert-unit-form-item {
    margin-bottom: 0 !important;
  }

  .alert-unit-form-item :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }

  .alert-units-inline {
    display: flex;
    flex-wrap: wrap;
    gap: var(--system-spacing-md, 12px);
    align-items: center;
    padding: var(--system-spacing-sm, 8px);
    background: var(--el-color-warning-light-9, #fdf6ec);
    border: 1px solid var(--el-color-warning-light-5, #f0c78a);
    border-radius: 6px;
    min-height: 48px;
  }

  .alert-unit-group {
    display: flex;
    align-items: center;
    gap: var(--system-spacing-xs, 4px);
    flex-shrink: 0;
  }

  .alert-inline-input {
    width: 80px !important;
  }

  .alert-inline-input :deep(.el-input__wrapper) {
    background: var(--el-color-white);
    border: 1px solid var(--el-color-warning-light-3, #e6a23c);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .alert-inline-input :deep(.el-input__wrapper:hover) {
    border-color: var(--el-color-warning);
    box-shadow: 0 2px 4px rgba(230, 162, 60, 0.2);
  }

  .alert-inline-input :deep(.el-input__wrapper.is-focus) {
    border-color: var(--el-color-warning);
    box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
  }

  .alert-unit-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-warning-dark-2, #b88230);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--system-spacing-xs, 4px);
  }

  .base-unit-text {
    color: var(--system-primary-color);
    font-weight: 600;
  }

  .base-unit-tag-inline {
    margin-left: var(--system-spacing-xs, 4px);
    font-size: 10px;
    height: 16px;
    line-height: 14px;
    padding: 0 4px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .alert-units-inline {
      flex-direction: column;
      align-items: stretch;
      gap: var(--system-spacing-sm, 8px);
    }

    .alert-unit-group {
      justify-content: space-between;
      padding: var(--system-spacing-xs, 4px) 0;
    }

    .alert-inline-input {
      width: 100px !important;
    }
  }

  /* 暗色主题适配 */
  [data-theme="dark"] .alert-units-inline {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
  }

  [data-theme="dark"] .alert-inline-input :deep(.el-input__wrapper) {
    background: var(--system-page-background);
    border-color: rgba(245, 158, 11, 0.4);
  }

  [data-theme="dark"] .alert-inline-input :deep(.el-input__wrapper:hover) {
    border-color: var(--system-page-warning-color);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
  }

  [data-theme="dark"] .alert-inline-input :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
  }

  [data-theme="dark"] .alert-unit-label {
    color: #d4a574;
  }

  [data-theme="dark"] .base-unit-text {
    color: var(--system-primary-color-light);
  }
</style>
