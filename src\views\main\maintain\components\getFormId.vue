<template>
  <div class="">
    <el-dialog
      v-model="dialogTableVisible"
      title="选择已配置的表单"
      ref="dialog"
      :before-close="handFormSave"
    >
      <el-input
        v-model="gridDatapage.keywords"
        placeholder="请输入表单名称"
        clearable
        @clear="getgridData"
        style="width: 30%; float: left"
      >
        <template #append>
          <el-button :icon="Search" @click="getgridData" />
        </template>
      </el-input>
      <span style="color: #f56c6c">注意：表单不能重复关联</span>
      <el-table
        :data="gridData"
        @current-change="handleChangeTable"
        highlight-current-row
      >
        <el-table-column property="id" label="id" width="100" />
        <el-table-column property="name" label="名称" width="200" />
        <el-table-column property="info" label="描述" />
        <el-table-column property="updateTime" label="更新时间" />
      </el-table>
      <el-pagination
        v-model:current-page="gridDatapage.page"
        :page-size="20"
        layout="total, prev, pager, next"
        :total="gridDatapage.total"
        @current-change="handleCurrentChange"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handFormSave">取消</el-button>
          <el-button type="primary" @click="handFormSave">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, defineProps, defineEmits } from 'vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import { getFormConfigList } from '@/api/systemFormConfig'
  interface val {
    id: number
  }

  const dialogTableVisible = ref(true)
  const gridData = ref()
  const formId = ref<number>()
  const gridDatapage = ref({
    keywords: '',
    page: 1,
    limit: 20,
    total: 0,
  })
  const handleChangeTable = (val: val) => {
    formId.value = val.id
  }
  const handleCurrentChange = (val: number) => {
    gridDatapage.value.page = val
    getgridData()
  }
  const emit = defineEmits(['setFormId'])
  const dialog = ref()
  const handFormSave = () => {
    emit('setFormId', formId.value)
  }
  //获取值
  const getgridData = () => {
    getFormConfigList(gridDatapage.value).then(res => {
      gridData.value = res.list
      gridDatapage.value.total = res.total
    })
  }
  getgridData()
</script>

<style lang="scss" scoped></style>
