import { number } from "echarts";

/**
 * 物流模板
 */
export interface LogisticsTemplateRes {
    /**
     * @name 城市ID, 多个逗号分割。全国 all
     */
    cityId: string | number[];
    /**
     * @name 首件
     */
    first: number;
    /**
     * @name 首件运费
     */
    firstPrice: number;
    /**
     * @name 续件
     */
    renewal: number;
    /**
     * @name 续件运费
     */
    renewalPrice: number;
    /**
     * @name 城市名称描述
     */
    title?: string;
    /**
     * @name 分组唯一值
     */
    uniqid?: string;
}
/**
 * 物流模板 免费
 */
export interface LogisticsTemplateFreeRes{
        cityId:string | number[];
        title:string;
        number: number;
        price: number
      
}

/**
 * 物流列表
 */
export interface LogisticsListParams {
    keywords?: string;
    limit?: number;
    page?: number;
}



/**
 * 物流列表
 */
export interface LogisticsListRes {
    /**
     * @name 指定包邮
     */
    appoint?: boolean;
    /**
     * @name 创建时间
     */
    createTime?: Date;
    /**
     * @name 编号
     */
    id?: number;
    /**
     * @name 模板名称
     */
    name?: string;
    /**
     * @name 排序
     */
    sort?: number;
    /**
     * @name 计费方式
     */
    type?: number;
    /**
     * @name 修改时间
     */
    updateTime?: Date;
}


/**
 * 城市树状图
 */
export interface CityTreeRes {
    /**
     * @name 区号
     */
    areaCode?: string;
    child?: CityTreeRes[];
    /**
     * @name 城市id
     */
    cityId?: number;
    /**
     * @name 创建时间
     */
    createTime?: Date;
    id?: number;
    /**
     * @name 是否展示
     */
    isShow?: boolean;
    /**
     * @name 纬度
     */
    lat?: string;
    /**
     * @name 省市级别
     */
    level?: number;
    /**
     * @name 经度
     */
    lng?: string;
    /**
     * @name 合并名称
     */
    mergerName?: string;
    /**
     * @name 名称
     */
    name?: string;
    /**
     * @name 父级id
     */
    parentId?: number;
    /**
     * @name 修改时间
     */
    updateTime?: Date;
}


/**
 * 新增物流
 */
export interface LogisticsParams {
    id:number;
    /**
     * @name 指定包邮
     */
    appoint: boolean;
    /**
     * @name 模板名称
     */
    name: string;
    /**
     * @name 指定包邮设置
     */
    shippingTemplatesFreeRequestList: ShippingTemplatesFreeRequest[];
    /**
     * @name 配送区域及运费
     */
    shippingTemplatesRegionRequestList: ShippingTemplatesRegionRequest[];
    /**
     * @name 排序
     */
    sort: number;
    /**
     * @name 计费方式 1(按件数), 2(按重量)，3(按体积)
     */
    type: number;
}

/**
 * 指定包邮设置
 */
export interface ShippingTemplatesFreeRequest {
    /**
     * @name 城市ID, 多个逗号分割。 全国 all
     */
    cityId: string;
    /**
     * @name 包邮件数
     */
    number: number;
    /**
     * @name 包邮金额
     */
    price: number;
    /**
     * @name 城市名称描述
     */
    title?: string;
}

/**
 * 配送区域及运费
 */
export interface ShippingTemplatesRegionRequest {
    /**
     * @name 城市ID, 多个逗号分割。全国 all
     */
    cityId: string;
    /**
     * @name 首件
     */
    first: number;
    /**
     * @name 首件运费
     */
    firstPrice: number;
    /**
     * @name 续件
     */
    renewal: number;
    /**
     * @name 续件运费
     */
    renewalPrice: number;
    /**
     * @name 城市名称描述
     */
    title?: string;
    /**
     * @name 分组唯一值
     */
    uniqid?: string;
}



/**
 * 物流详情
 */
export interface LogisticsInfoRes {
    /**
     * @name 指定包邮
     */
    appoint?: boolean;
    /**
     * @name 创建时间
     */
    createTime?: Date;
    /**
     * @name 编号
     */
    id?: number;
    /**
     * @name 模板名称
     */
    name?: string;
    /**
     * @name 排序
     */
    sort?: number;
    /**
     * @name 计费方式
     */
    type?: number;
    /**
     * @name 修改时间
     */
    updateTime?: Date;
}