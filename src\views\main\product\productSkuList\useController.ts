import type { AttrValueResponse } from '@/type/order.type'
import { ref, type Ref } from 'vue'

//智能订单生成数组
export const intelligencePurchaseOrderList: Ref<AttrValueResponse[]> = ref([])

// 智能订单标识符，用于标识是否来自智能生成
export const intelligenceOrderFlag: Ref<boolean> = ref(false)

// 设置智能订单数据并标记为智能生成
export const setIntelligenceOrder = (data: AttrValueResponse[]) => {
  intelligencePurchaseOrderList.value = data
  intelligenceOrderFlag.value = true
}

// 清除智能订单标识
export const clearIntelligenceOrder = () => {
  intelligenceOrderFlag.value = false
}
