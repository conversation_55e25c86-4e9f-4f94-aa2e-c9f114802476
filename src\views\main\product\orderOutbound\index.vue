<template>
  <div class="layout-container" style="padding: 15px">
    <el-tabs v-model="query.type" class="demo-tabs" @tab-click="clickTab">
      <el-tab-pane
        :label="item.name + `(${item.count})`"
        :name="item.type"
        v-for="item in tabsList"
        :key="item.type"
      ></el-tab-pane>
    </el-tabs>
    <ProductSteps :active-index="activeIndex" :step-title="stepTitle" />
    <ProductSearch
      v-if="activeIndex === 0"
      v-model:keywords="query.keywords"
      v-model:cateId="query.cateId"
      :options="options"
      @search="getTableData(true)"
    />
    <div class="layout-container-table">
      <ProductTable
        ref="productTableRef"
        v-show="activeIndex === 0"
        v-model:page="page"
        :loading="loading"
        :data="tableData"
        @getTableData="getTableData"
        @selection-change="handleSelectionChange"
      />
      <skuInfo
        ref="skuInfoRef"
        v-show="activeIndex === 1"
        :list="handleSkuList"
        :type="'2'"
        @addActiveIndex="handleSubmitSuccess"
        @removeItem="handleRemoveItem"
      ></skuInfo>
      <!-- 第三页：完成页面 -->
      <div v-show="activeIndex === 2" class="success-page">
        <div class="success-container">
          <div class="success-card">
            <div class="success-icon-wrapper">
              <div class="success-icon-bg">
                <div class="success-icon">
                  <svg viewBox="0 0 1024 1024" width="48" height="48">
                    <path
                      fill="#ffffff"
                      d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <div class="success-content">
              <h2 class="success-title">出库申请提交成功</h2>
              <p class="success-subtitle">您的出库申请已成功提交</p>
              <div class="success-details">
                <div class="detail-item">
                  <span class="detail-label">申请时间：</span>
                  <span class="detail-value">
                    {{ new Date().toLocaleString() }}
                  </span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">处理状态：</span>
                  <span class="detail-value status-processing">
                    系统处理完成
                  </span>
                </div>
              </div>
            </div>

            <div class="success-actions">
              <el-button
                type="primary"
                size="large"
                class="action-button primary-button"
                @click="startNewProcess"
              >
                <span class="button-icon">+</span>
                开始新的出库流程
              </el-button>
              <el-button
                size="large"
                class="action-button secondary-button"
                @click="cancel"
              >
                <span class="button-icon">←</span>
                返回产品列表
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <StepNavigation
      :active-index="activeIndex"
      :selected-count="allSelectedData.length"
      @next="clickNext"
      @prev="clickPrev"
      @submit="handleRules"
      @cancel="cancel"
    />

    <!-- 确认弹窗 -->
    <OutboundConfirmDialog
      v-model:visible="showConfirmDialog"
      :phone-number="confirmDialogData.phoneNumber"
      :remarks="confirmDialogData.remarks"
      :products="confirmDialogData.products"
      :gift-list="confirmDialogData.giftList"
      :customer-name="confirmDialogData.customerName"
      :examine-approve="confirmDialogData.examineApprove"
      :examine="confirmDialogData.examine"
      :document-preparation="confirmDialogData.documentPreparation"
      :consignee="confirmDialogData.consignee"
      :driver="confirmDialogData.driver"
      @confirm="handleConfirmSubmit"
      @cancel="handleConfirmCancel"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, computed, onMounted, nextTick } from 'vue'
  import { Page } from '@/components/table/type'
  import skuInfo from '../productSkuList/skuInfo.vue'
  import { ElMessage } from 'element-plus'
  import { getAttrValueList } from '@/api/product'
  import { treeCategroy } from '@/api/categoryApi'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRouter } from 'vue-router'
  import { TreeCategroyRes } from '@/type/category.type'
  import type { ProductListRes } from '@/type/product.type'
  // 导入智能订单数据
  import {
    intelligencePurchaseOrderList,
    intelligenceOrderFlag,
    clearIntelligenceOrder,
  } from '../productSkuList/useController'
  // 导入新组件
  import {
    ProductSteps,
    ProductSearch,
    ProductTable,
    StepNavigation,
  } from '../productSkuList/components'
  import OutboundConfirmDialog from './OutboundConfirmDialog.vue'
  const router = useRouter()

  // 添加表格引用
  const productTableRef = ref()

  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    cateId: '',
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ProductListRes[]>([])
  const activeIndex = ref(0)

  // 存储外部传入的预选数据
  const preSelectedData = ref<any[]>([])

  // 存储所有已选中的数据（跨页面缓存）
  const allSelectedData = ref<any[]>([])

  // 确认弹窗相关状态
  const showConfirmDialog = ref(false)
  const confirmDialogData = ref({
    phoneNumber: '',
    remarks: '',
    products: [],
    giftList: [] as any[],
    // 新增字段
    customerName: '',
    examineApprove: '',
    examine: '',
    documentPreparation: '',
    consignee: '',
    driver: '',
  })

  // 去重函数，确保数据唯一性
  const removeDuplicates = (data: any[]) => {
    const seen = new Set()
    return data.filter(item => {
      const key = item.id || `${item.productId}-${item.suk}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  // 处理外部传入的数据
  const initPreSelectedData = () => {
    // 检查是否来自复制订单
    const copyOrderData = sessionStorage.getItem('copyOrderData')
    if (copyOrderData) {
      try {
        const parsedData = JSON.parse(copyOrderData)
        if (parsedData.skuList && parsedData.skuList.length > 0) {
          // 处理复制的订单数据
          const processedData = parsedData.skuList.map((item: any) => ({
            ...item,
            // 确保数据格式正确
            changeUnitNums: item.changeUnitNums || item.unitNums || [],
          }))

          const uniqueData = removeDuplicates(processedData)
          preSelectedData.value = uniqueData
          allSelectedData.value = uniqueData
          handleSkuList.value = [...uniqueData]
          isSkuListInitialized.value = true
          activeIndex.value = 1

          // 设置复制的电话、备注和新增字段信息
          nextTick(() => {
            if (skuInfoRef.value && skuInfoRef.value.form) {
              skuInfoRef.value.form.phone = parsedData.phone || ''
              skuInfoRef.value.form.mark = parsedData.mark || ''
              // 设置新增的6个字段
              skuInfoRef.value.form.customerName = parsedData.customerName || ''
              skuInfoRef.value.form.examineApprove = parsedData.examineApprove || ''
              skuInfoRef.value.form.examine = parsedData.examine || ''
              skuInfoRef.value.form.documentPreparation = parsedData.documentPreparation || ''
              skuInfoRef.value.form.consignee = parsedData.consignee || ''
              skuInfoRef.value.form.driver = parsedData.driver || ''
              // 设置赠品列表
              if (parsedData.giftList && parsedData.giftList.length > 0) {
                skuInfoRef.value.form.giftList = parsedData.giftList.map((item: any) => ({
                  ...item,
                  // 确保赠品数据格式正确
                  changeUnitNums: item.changeUnitNums || item.unitNums || [],
                  unitPrice: 0, // 赠品单价为0
                  price: 0, // 赠品金额为0
                  isGift: true // 标识为赠品
                }))
              }
            }
          })

          // 清除sessionStorage中的数据，避免重复使用
          sessionStorage.removeItem('copyOrderData')

          // 根据复制的数据内容显示不同的提示信息
          let successMessage = '订单数据已复制，请确认出库信息'
          if (parsedData.giftList && parsedData.giftList.length > 0) {
            successMessage += `（包含 ${parsedData.giftList.length} 个赠品）`
          }
          ElMessage.success(successMessage)
          return
        }
      } catch (error) {
        console.error('解析复制订单数据失败:', error)
        sessionStorage.removeItem('copyOrderData')
      }
    }

    // 检查是否来自智能生成
    if (
      intelligenceOrderFlag.value &&
      intelligencePurchaseOrderList.value.length > 0
    ) {
      const uniqueData = removeDuplicates([
        ...intelligencePurchaseOrderList.value,
      ])
      preSelectedData.value = uniqueData
      allSelectedData.value = uniqueData
      activeIndex.value = 1

      // 如果有预选数据，设置到handleSkuList中
      if (preSelectedData.value.length > 0) {
        handleSkuList.value = [...preSelectedData.value]
        isSkuListInitialized.value = true // 标记已初始化
      }

      // 清除智能订单标识，避免重复使用
      clearIntelligenceOrder()
    }
  }

  // 获取表格数据
  // params <init> boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init?: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: any = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getAttrValueList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)

        // 如果有缓存的选中数据，自动选中对应的行
        if (allSelectedData.value.length > 0) {
          nextTick(() => {
            setTimeout(() => {
              autoSelectRowsFromCache()
            }, 100) // 增加延迟确保表格完全渲染
          })
        }
      })
      .catch(() => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 自动选中行的方法
  const autoSelectRows = () => {
    // 尝试多种方式访问表格实例
    let elTable = null

    if (productTableRef.value?.table?.table) {
      elTable = productTableRef.value.table.table
    } else if (productTableRef.value?.table) {
      elTable = productTableRef.value.table
    } else if (productTableRef.value?.$refs?.table) {
      elTable = productTableRef.value.$refs.table
    }

    if (!elTable) {
      return
    }

    // 清除之前的选择
    if (elTable.clearSelection) {
      elTable.clearSelection()
    }

    // 根据预选数据选中对应的行
    preSelectedData.value.forEach(preSelectedItem => {
      const matchedRow = tableData.value.find(
        row =>
          row.id === preSelectedItem.id ||
          ((row as any).productId === preSelectedItem.productId &&
            (row as any).suk === preSelectedItem.suk)
      )

      if (matchedRow && elTable.toggleRowSelection) {
        // 先更新匹配行的数据，使用传入项的数据
        const updatedRow = { ...matchedRow, ...preSelectedItem }
        // 找到在tableData中的索引并更新
        const index = tableData.value.findIndex(row => row.id === matchedRow.id)
        if (index !== -1) {
          tableData.value[index] = updatedRow
        }

        // 使用更新后的行进行选中操作
        elTable.toggleRowSelection(updatedRow, true)
      }
    })
  }

  // 从缓存中自动选中行的函数
  const autoSelectRowsFromCache = () => {
    // 尝试多种方式访问表格实例
    let elTable = null

    if (productTableRef.value?.table?.table) {
      elTable = productTableRef.value.table.table
    } else if (productTableRef.value?.table) {
      elTable = productTableRef.value.table
    } else if (productTableRef.value?.$refs?.table) {
      elTable = productTableRef.value.$refs.table
    }

    if (!elTable) {
      return
    }

    // 根据全局缓存数据选中对应的行
    allSelectedData.value.forEach(cachedItem => {
      const matchedRow = tableData.value.find(
        row =>
          row.id === cachedItem.id ||
          ((row as any).productId === cachedItem.productId &&
            (row as any).suk === cachedItem.suk)
      )

      if (matchedRow && elTable.toggleRowSelection) {
        // 使用匹配的行进行选中操作
        elTable.toggleRowSelection(matchedRow, true)
      }
    })
  }

  const tabsList = ref<ProductHeaderRes[]>([])

  //切换tab
  const clickTab = () => {
    getTableData(true)
  }
  //商品分类选择框
  const options = ref<TreeCategroyRes[]>([])
  treeCategroy({ type: 1, status: -1 }).then(res => [(options.value = res)])

  const handleSkuList = ref<any[]>([])
  // 标记是否已经初始化过第二步的数据
  const isSkuListInitialized = ref(false)

  const handleSelectionChange = (val: ProductListRes[]) => {
    // 更新全局缓存，但不要轻易修改 handleSkuList
    // 只更新全局缓存用于表格选中状态的维护
    const currentPageIds = tableData.value.map(item => item.id)

    // 移除当前页面的数据
    allSelectedData.value = allSelectedData.value.filter(
      item => !currentPageIds.includes(item.id)
    )

    // 添加当前页面新选中的数据
    allSelectedData.value.push(...val)

    // 去重
    allSelectedData.value = removeDuplicates(allSelectedData.value)
  }

  const clickNext = () => {
    if (!allSelectedData.value.length && activeIndex.value === 0) {
      return ElMessage.error('请选择至少一个货品！')
    } else {
      // 从第一步进入第二步时，需要同步数据
      if (activeIndex.value === 0) {
        if (!isSkuListInitialized.value) {
          // 第一次进入第二步，使用全局缓存的数据
          handleSkuList.value = [...allSelectedData.value]
          isSkuListInitialized.value = true
        } else {
          // 已经初始化过，需要同步新添加的产品，但保持已有产品的填写数据
          const existingIds = handleSkuList.value.map(item => item.id)
          const newItems = allSelectedData.value.filter(
            item => !existingIds.includes(item.id)
          )
          const removedIds = existingIds.filter(
            id => !allSelectedData.value.some(item => item.id === id)
          )

          // 移除不再选中的产品
          handleSkuList.value = handleSkuList.value.filter(
            item => !removedIds.includes(item.id)
          )

          // 添加新选中的产品
          handleSkuList.value.push(...newItems)
        }
      }
      activeIndex.value++
    }
  }

  const clickPrev = () => {
    // 返回上一步时，保持智能生成的数据和已选中的数据
    // 不要清空或重置 handleSkuList，保持用户填写的数据
    activeIndex.value--

    // 如果返回到第一步，确保表格能正确显示选中状态
    // 但不要重新设置 handleSkuList，避免丢失用户填写的数据
    if (activeIndex.value === 0) {
      // 由于使用了 reserve-selection，表格会自动恢复选中状态
      // 我们不需要重新设置 handleSkuList，这样可以保持用户在第二步填写的数据
    }
  }
  const cancel = () => {
    router.push('/product/productSkuList')
  }
  const stepTitle = computed(() => {
    return `填写出库信息`
  })
  const skuInfoRef = ref()
  const handleRules = async () => {
    try {
      // 验证表单数据
      if (!skuInfoRef.value) return

      // 获取表单数据进行验证
      const formData = skuInfoRef.value.form || {}

      // 验证必填字段
      if (!formData.phone) {
        ElMessage.error('请输入电话号码')
        return
      }

      // 验证是否有出库商品
      if (
        !formData.outboundEntityList ||
        formData.outboundEntityList.length === 0
      ) {
        ElMessage.error('请选择出库商品')
        return
      }

      // 验证出库数量
      const hasValidQuantity = formData.outboundEntityList.some((item: any) => {
        const nums = item.changeUnitNums || item.unitNums || []
        return nums.some((num: any) => num > 0)
      })

      if (!hasValidQuantity) {
        ElMessage.error('请填写出库数量')
        return
      }

      // 验证赠品数量（如果有赠品的话）
      if (formData.giftList && formData.giftList.length > 0) {
        const hasValidGiftQuantity = formData.giftList.some((item: any) => {
          const nums = item.changeUnitNums || item.unitNums || []
          return nums.some((num: any) => num > 0)
        })

        if (!hasValidGiftQuantity) {
          ElMessage.error('请填写赠品的出库数量')
          return
        }
      }

      // 准备确认弹窗数据
      confirmDialogData.value = {
        phoneNumber: formData.phone,
        remarks: formData.mark || '',
        products: formData.outboundEntityList,
        giftList: formData.giftList || [],
        customerName: formData.customerName || '',
        examineApprove: formData.examineApprove || '',
        examine: formData.examine || '',
        documentPreparation: formData.documentPreparation || '',
        consignee: formData.consignee || '',
        driver: formData.driver || '',
      }

      // 显示确认弹窗
      showConfirmDialog.value = true
    } catch (e: any) {
      ElMessage.error(e.message || '验证失败')
    }
  }

  // 确认弹窗确认处理
  const handleConfirmSubmit = async () => {
    try {
      await skuInfoRef.value.onSubmit()
      // 注意：不在这里清空数据，等待 handleSubmitSuccess 被调用
    } catch (e: any) {
      ElMessage.error(e.message)
      // 提交失败时不清空数据，不返回第一页，让用户可以修改后重新提交
    }
  }

  // 确认弹窗取消处理
  const handleConfirmCancel = () => {
    showConfirmDialog.value = false
  }

  // 处理提交成功的回调
  const handleSubmitSuccess = () => {
    // 提交成功后跳转到第三页（完成页面）
    activeIndex.value = 2
  }

  // 处理删除项目的回调
  const handleRemoveItem = (removedItem: any) => {
    // 从 handleSkuList 中移除
    const skuIndex = handleSkuList.value.findIndex(
      item => item.id === removedItem.id
    )
    if (skuIndex > -1) {
      handleSkuList.value.splice(skuIndex, 1)
    }

    // 从 allSelectedData 中移除
    const allIndex = allSelectedData.value.findIndex(
      item => item.id === removedItem.id
    )
    if (allIndex > -1) {
      allSelectedData.value.splice(allIndex, 1)
    }

    // 无论在哪一步，都要更新第一步表格的选中状态
    // 因为用户可能会返回第一步查看
    nextTick(() => {
      // 如果当前在第一步，直接更新选中状态
      if (activeIndex.value === 0) {
        autoSelectRowsFromCache()
      } else {
        // 如果不在第一步，更新缓存的选中状态
        // 当用户返回第一步时会自动应用这些状态
        const elTable =
          productTableRef.value?.table?.table || productTableRef.value?.table
        if (elTable && elTable.toggleRowSelection) {
          const tableList = elTable.getSelectionRows()
          // 查找要取消选中的行
          const rowToDeselect = tableList.find(
            (row: any) => row.id === removedItem.id
          )
          if (rowToDeselect) {
            elTable.toggleRowSelection(rowToDeselect, false)
          }
        }
      }
    })
  }

  // 开始新的出库流程
  const startNewProcess = () => {
    // 清空所有数据并返回第一页
    clearAllData()
  }

  // 清空所有数据的函数，只在提交成功后调用
  const clearAllData = () => {
    handleSkuList.value = []
    allSelectedData.value = []
    preSelectedData.value = []
    isSkuListInitialized.value = false
    activeIndex.value = 0

    // 清空表格的选中状态
    nextTick(() => {
      const elTable =
        productTableRef.value?.table?.table || productTableRef.value?.table
      if (elTable && elTable.clearSelection) {
        elTable.clearSelection()
      }
    })
  }
  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
          tabsList.value = res
          getTableData(true)
      })*/
    getTableData(true)
  }

  // 外部设置预选数据的方法
  const setPreSelectedData = (data: any[]) => {
    preSelectedData.value = data
    handleSkuList.value = [...data]

    // 如果表格已经加载，立即执行自动选中
    if (tableData.value.length > 0) {
      nextTick(() => {
        autoSelectRows()
      })
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    // 初始化预选数据
    initPreSelectedData()
  })

  // 暴露方法供外部调用
  defineExpose({
    setPreSelectedData,
  })

  //获取tab栏
  getTabsHeader()
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }

  .flex-center {
    margin-left: 80px;
  }

  .success-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .success-container {
    width: 100%;
    max-width: 600px;
  }

  .success-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 48px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .success-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #67c23a 0%, #85ce61 100%);
  }

  .success-icon-wrapper {
    margin-bottom: 32px;
  }

  .success-icon-bg {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 12px 32px rgba(103, 194, 58, 0.4);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
    }
  }

  .success-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .success-content {
    margin-bottom: 40px;
  }

  .success-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
  }

  .success-subtitle {
    font-size: 16px;
    color: #7f8c8d;
    margin-bottom: 32px;
    line-height: 1.5;
  }

  .success-details {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin: 0 auto;
    max-width: 400px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
  }

  .detail-item:not(:last-child) {
    border-bottom: 1px solid #e9ecef;
  }

  .detail-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
  }

  .detail-value {
    font-size: 14px;
    color: #495057;
    font-weight: 600;
  }

  .status-processing {
    color: #17a2b8;
    background: #d1ecf1;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
  }

  .success-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-button {
    min-width: 180px;
    height: 48px;
    border-radius: 24px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .primary-button {
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    border: none;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  }

  .primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4);
  }

  .secondary-button {
    background: #ffffff;
    border: 2px solid #e9ecef;
    color: #6c757d;
  }

  .secondary-button:hover {
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
  }

  .button-icon {
    font-size: 16px;
    font-weight: bold;
  }
</style>
