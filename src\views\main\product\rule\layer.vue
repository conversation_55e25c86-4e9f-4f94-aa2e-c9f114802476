<template>
  <Layer :layer="layer" @confirm="submit(formRef)" ref="layerDom">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" style="margin-right:30px;">
      <el-form-item label="规格名称" prop="ruleName">
        <el-input v-model="form.ruleName" placeholder="请输入名称"></el-input>
      </el-form-item>

      <div v-for="(item, index) in ruleList" style="margin-left: 120px;">
        <div> <el-tag effect="dark" closable @close="nameClose(index)">{{ item.value
        }}</el-tag></div>
        <div style="margin-bottom: 10px;"> <el-tag :closable="ruleList[index]['detail'].length > 1"
            v-for="(tag, tagIndex) in  item.detail" @close="valueClose(index, tagIndex)" style="margin-right: 10px;">{{
              tag }}</el-tag>
          <el-input v-model="ruleList[index]['addValue']" placeholder="请输入属性名称" style="width: 200px;">
            <template #append><el-button type="primary"
                @click="addRuleValue(index, ruleList[index]['addValue'])">添加</el-button>
            </template></el-input>
        </div>
      </div>
      <div style="margin:40px 0 20px 120px">
        <el-input v-model="ruleForm.value" placeholder="请输入规格名称" style="margin-bottom: 10px;"></el-input>
        <el-input v-model="ruleForm.detail" placeholder="请输入属性名称"></el-input>
        <el-button type="primary" @click="addRules" style="margin-top: 20px;">添加新规格</el-button>
      </div>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ref, defineProps, defineEmits, reactive } from 'vue'
import Layer from '@/components/layer/index.vue'
import { saveProductRule, updateProductRule } from '@/api/product'
import { ElMessage } from 'element-plus'
import type { RuleParams } from '@/type/product.type'
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const emit = defineEmits(['getTableData'])
const formRef = ref<FormInstance>()
const layerDom: Ref<LayerType | null> = ref(null)
const form = ref({
  ruleName: '',
  ruleValue: null
})
const rules = reactive<FormRules>({
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
})

function init() { // 用于判断新增还是编辑功能
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
    ruleList.value=JSON.parse(form.value.ruleValue) 
  } else {
  }
}
/* 规格值新增/编辑自定义 */
const ruleList = ref<any>([])
const ruleForm = ref({ value: '', detail: '', })
const addRules = () => {
  if (ruleForm.value.value && ruleForm.value.detail) {
    ruleList.value.push({
      value: ruleForm.value.value,
      detail: [ruleForm.value.detail],
      addValue: ''
    })
    ruleForm.value.value = ''
    ruleForm.value.detail = ''
  } else {
    ElMessage.warning('规格名称或规格值不能为空')
  }
}
const valueClose = (index: number, tagIndex: number) => {
  ruleList.value[index]['detail'].splice(tagIndex, 1)
}

const nameClose = (index: number) => {
  ruleList.value.splice(index, 1)
}
const addRuleValue = (index: number, str: string) => {
  ruleList.value[index]['detail'].push(str)
  ruleList.value[index]['addValue'] = ''
}
const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {

      let params: RuleParams = form.value
      let arr = ruleList.value.map(item => {
        let { value, detail } = item
        let obj = {
          value,
          detail
        }
        return obj
      })
      params.ruleValue = JSON.stringify(arr)
      if (ruleList.value.length == 0) {
        ElMessage({
          type: 'warning',
          message: '请至少添加一条规格'
        })
        return
      }
      if (props.layer.row) {
        updateForm(params)
      } else {
        addForm(params)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
const addForm = (params: RuleParams) => {
  saveProductRule(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
}

const updateForm = (params: RuleParams) => {

  updateProductRule(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
}

init()

</script>

<style lang="scss" scoped></style>