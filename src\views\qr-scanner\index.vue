<template>
  <div class="qr-scanner-container">
    <!-- 摄像头预览渲染区域 -->
    <video ref="videoEl" class="scanner-video" autoplay playsinline></video>

    <!-- 遮罩和扫描框UI -->
    <div class="scanner-overlay">
      <div class="scan-box">
        <div class="corner top-left"></div>
        <div class="corner top-right"></div>
        <div class="corner bottom-left"></div>
        <div class="corner bottom-right"></div>
        <div class="scan-line"></div>
      </div>
      <p class="scan-tip">将二维码放入框内，即可自动扫描</p>
    </div>

    <!-- 底部操作按钮 -->
    <div class="actions-bar">
      <button class="action-btn" @click="handleToggleFlashlight">
        <Zap :class="{ 'active': isFlashlightOn }" />
        <span>闪光灯</span>
      </button>
      <button class="action-btn" @click="openGallery">
        <ImageIcon />
        <span>相册</span>
      </button>
      <button class="action-btn" @click="goBack">
        <ArrowLeft />
        <span>返回</span>
      </button>
      <input type="file" ref="fileInputRef" @change="handleFileScan" accept="image/*" class="hidden-input" />
    </div>

    <!-- 错误弹窗 -->
    <div v-if="error" class="result-dialog-overlay">
      <div class="result-dialog">
        <h3>扫描失败</h3>
        <p class="result-text">{{ error }}</p>
        <div class="dialog-buttons">
          <button @click="resetScanner" class="dialog-button secondary">
            重试
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Zap, ImageIcon, ArrowLeft } from 'lucide-vue-next';
import { ElMessage } from 'element-plus';
import { useBarcodeScanner } from '@/composables/useBarcodeScanner';

const router = useRouter();

const fileInputRef = ref(null);
const isFlashlightOn = ref(false);

// 使用扫码 Hook
const { videoEl, result, error, start, stop, scanFromFile, toggleFlashlight } = useBarcodeScanner();

// 监听扫码结果，成功后关闭扫码页面并跳转到商品详情页
watch(result, (newResult) => {
  if (newResult) {
    // 停止扫描
    stop();

    // 弹出扫码结果
    ElMessage({
      type: 'success',
      message: `扫码成功：${newResult}`,
      duration: 1500,
      showClose: true
    });

    // 立即跳转到商品详情页，这会关闭当前扫码页面
    setTimeout(() => {
      // 将扫码结果作为商品ID，跳转到商品详情页
      // 使用 replace 而不是 push，这样可以替换当前页面而不是添加到历史记录
      router.replace({
        path: '/qr-scanner/product-detail',
        query: {
          id: newResult,
          from: 'scan' // 标记来源为扫码
        }
      });
    }, 300);
  }
});

// 从相册选择图片进行扫描
const handleFileScan = async (event) => {
  const file = event.target.files?.[0];
  if (file) {
    try {
      const decodedText = await scanFromFile(file);
      if (decodedText) {
        result.value = decodedText;
      } else {
        error.value = "无法识别图片中的二维码。";
      }
    } catch (err) {
      error.value = "无法识别图片中的二维码。";
    }
  }
};

// 打开文件选择器
const openGallery = () => {
  fileInputRef.value?.click();
};

// 切换闪光灯
const handleToggleFlashlight = async () => {
  try {
    const newState = await toggleFlashlight();
    isFlashlightOn.value = newState;
  } catch (err) {
    error.value = "无法控制闪光灯，您的设备可能不支持。";
  }
};

// 重置扫描器
const resetScanner = () => {
  result.value = null;
  error.value = null;
  start(); // 重新开始扫描
};

// 返回上一页
const goBack = () => {
  router.back();
};



onMounted(() => {
  start();
});
</script>

<style lang="scss" scoped>
.qr-scanner-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #000;

  .scanner-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: none;
  }
}

.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
}

.scan-box {
  position: relative;
  width: 250px;
  height: 250px;

  .corner {
    position: absolute;
    width: 24px;
    height: 24px;
    border: 4px solid white;
  }
  .top-left { top: -4px; left: -4px; border-right: none; border-bottom: none; border-top-left-radius: 8px; }
  .top-right { top: -4px; right: -4px; border-left: none; border-bottom: none; border-top-right-radius: 8px; }
  .bottom-left { bottom: -4px; left: -4px; border-right: none; border-top: none; border-bottom-left-radius: 8px; }
  .bottom-right { bottom: -4px; right: -4px; border-left: none; border-top: none; border-bottom-right-radius: 8px; }
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, transparent, #00ff00, transparent);
  box-shadow: 0 0 8px 1px rgba(0, 255, 0, 0.7);
  animation: scan 2.5s linear infinite alternate;
}

@keyframes scan {
  from { transform: translateY(0); }
  to { transform: translateY(248px); }
}

.scan-tip {
  margin-top: 24px;
  color: #e0e0e0;
  font-size: 14px;
}

// 移除scan-type样式

.actions-bar {
  position: absolute;
  bottom: 10vh;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 48px;
  z-index: 20;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  
  svg {
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
    transition: color 0.2s;

    &.active {
      color: #fef08a; // yellow-200
      filter: drop-shadow(0 0 5px #fef08a);
    }
  }

  span {
    font-size: 12px;
  }
}

.hidden-input {
  display: none;
}

.result-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
}

.result-dialog {
  background: white;
  color: #333;
  padding: 24px;
  border-radius: 12px;
  width: 80%;
  max-width: 350px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);

  h3 {
    margin: 0 0 12px;
    font-size: 18px;
    font-weight: 600;
  }

  .result-text {
    margin: 0 0 24px;
    font-size: 14px;
    word-break: break-all;
    color: #555;
  }

  .dialog-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .operation-buttons {
    display: flex;
    gap: 12px;
  }

  .dialog-button {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;

    &.primary {
      background-color: #3b82f6; // blue-500
      color: white;

      &:hover {
        background-color: #2563eb; // blue-600
      }
    }

    &.secondary {
      background-color: #f3f4f6; // gray-100
      color: #374151; // gray-700

      &:hover {
        background-color: #e5e7eb; // gray-200
      }
    }

    &.inbound {
      background-color: #10b981; // green-500
      color: white;

      &:hover {
        background-color: #059669; // green-600
      }
    }

    &.outbound {
      background-color: #3b82f6; // blue-500
      color: white;

      &:hover {
        background-color: #2563eb; // blue-600
      }
    }
  }
}
</style>
