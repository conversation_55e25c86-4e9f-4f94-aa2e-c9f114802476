<template>
  <div>
    <el-button
      type="primary"
      size="large"
      style="width: 100px"
      @click="handleNext"
      v-if="activeIndex !== 1 && activeIndex < 2"
    >
      点击下一步
    </el-button>
    <el-button
      type="primary"
      v-if="activeIndex !== 0 && activeIndex < 2"
      size="large"
      style="width: 100px"
      @click="handlePrev"
    >
      点击上一步
    </el-button>
    <el-button
      type="primary"
      v-if="activeIndex === 1"
      size="large"
      style="width: 100px"
      @click="handleSubmit"
    >
      提交
    </el-button>
    <el-button
      size="large"
      v-if="activeIndex < 2"
      style="width: 100px"
      @click="handleCancel"
    >
      取消
    </el-button>
  </div>
</template>

<script lang="ts" setup>
  defineProps<{
    activeIndex: number
    selectedCount: number
  }>()

  const emit = defineEmits<{
    next: []
    prev: []
    submit: []
    cancel: []
  }>()

  const handleNext = () => {
    emit('next')
  }

  const handlePrev = () => {
    emit('prev')
  }

  const handleSubmit = () => {
    emit('submit')
  }

  const handleCancel = () => {
    emit('cancel')
  }
</script>

<style lang="scss" scoped></style>
