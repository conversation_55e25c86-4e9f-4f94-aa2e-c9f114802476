<template>
  <component :is="type" v-bind="linkProps(to)" @click="hideMenu">
    <slot></slot>
  </component>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from "vuex";
import { useRouter } from 'vue-router'
import routesList from '@/router/routesList'
export default defineComponent({
  name: 'appLink',
  props: {
    to: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const store = useStore();
    const isCollapse = computed(() => store.state.app.isCollapse);
    const linkProps = (to) => {
      return {
        to: to
      }
    }
    let mb = p => o => p.map(c => o = (o || {})[c]) && o
    const createComponents = (path: string) => {
      let list = path.split('/')
      list.shift()
      let getPath = mb(list)
      if (getPath(routesList)) return getPath(routesList)
      return false
      // return createNameComponent(() => import('@/views/main/dashboard/index.vue'))
    }
    const hideMenu = () => {
      /* 新增的路由路径不存在跳404 */
      if (!createComponents(props.to)) {
        router.push({ path: '/404' })
      }
      if (document.body.clientWidth <= 1000 && !isCollapse.value) {
        store.commit("app/isCollapseChange", true);
      }
    };
    return {
      type: "router-link",
      linkProps,
      hideMenu
    }
  }
})
</script>
<style lang="">
  
</style>