import { h, ref } from "vue"
import {
  ElInput,
  ElRadioGroup,
  ElInputNumber,
  ElSwitch,
  ElSelect,
  ElCheckboxGroup,
  ElCascader,
  ElSlider,
  ElDatePicker,
} from "element-plus"
import SelfUpload from "@/components/uploadPicture/forGenrator/index.vue"
import tinymce from "@/components/tinymce/forGenrator/index.vue"
import { getPicUrl } from "@/utils/index"
function vModel(self, dataObject, defaultValue) {
  dataObject.props.value = defaultValue
  // dataObject.on.input = (val) => {
  //   self.$emit("input", val)

  // }
}
const componentChild = {}
/**
 * 将./slots中的文件挂载到对象componentChild上
 * 文件名为key，对应JSON配置中的__config__.tag
 * 文件内容为value，解析JSON配置中的__slot__
 */
const keys = import.meta.globEager("./slots/*.jsx")
Object.keys(keys).forEach((key) => {
  const tag = key.replace(/^\.\/(.*)\.\w+$/, "$1").slice(6)
  const value = keys[key].default
  componentChild[tag] = value
})

export default {
  render() {
    const dataObject = {
      attrs: {},
      props: {},
      on: {},
      style: {},
    }
    const confClone = JSON.parse(JSON.stringify(this.conf))
    const children = []
    const childObjs = componentChild[confClone.__config__.tag]
    if (childObjs) {
      Object.keys(childObjs).forEach((key) => {
        const childFunc = childObjs[key]

        if (confClone.__slot__ && confClone.__slot__[key]) {
          // console.log(confClone);
          children.push(childFunc(h, confClone, key))
        }
      })
    }

    Object.keys(confClone).forEach((key) => {
      const val = confClone[key]
      if (key === "__vModel__") {
        vModel(this, dataObject, confClone.__config__.defaultValue)
      } else if (dataObject[key]) {
        dataObject[key] = { ...dataObject[key], ...val }
      } else {
        dataObject.attrs[key] = val
      }
    })

    delete dataObject.attrs.__config__
    delete dataObject.attrs.__slot__
    delete dataObject.attrs.size
    //修改这里的input判断是element的什么组件
    let tagend = null
    // 如果是数字输入框转换为数字类型
    if (this.conf.__config__.tag == "el-input-number") {
      dataObject.props.value = Number(dataObject.props.value)
    }
    switch (this.conf.__config__.tag) {
      case "el-input":
        tagend = ElInput
        break
      case "el-switch":
        tagend = ElSwitch
        break
      case "el-radio-group":
        tagend = ElRadioGroup
        break
      case "el-input-number":
        tagend = ElInputNumber
        break
      case "el-date-time-picker":
        tagend = ElDatePicker
        break
      case "el-select":
        tagend = ElSelect
        break
      case "tinymce":
        tagend = tinymce
        dataObject.attrs.value = dataObject.props.value
        break
      case "self-upload":
        tagend = SelfUpload
        dataObject.attrs.value = dataObject.props.value
        // console.log(dataObject.props.value)
        // dataObject.props.value = getPicUrl() + dataObject.props.value
        break
    }
    // console.log(dataObject.props.value);
    // console.log(children)

    const modelObject = {
      ...dataObject.attrs,
          /* 时间选择器传参类型  先全部都加进去 日后优化 */
          format: 'YYYY-MM-DD HH:mm:ss',
          'value-format': 'YYYY-MM-DD HH:mm:ss',
      modelValue: dataObject.props.value,
      "onUpdate:modelValue": (newValue) => {
        dataObject.props.value = newValue
        return newValue
      },
    }
    return h(tagend, modelObject, () => {
      return children
    })
  },
  props: ["conf"],
}
// [Vue warn]: Invalid prop: type check failed for prop "modelValue". Expected String | Number | Boolean, got Object
//   at <ElRadioGroup disabled=false modelValue=
// Object { isTrusted: true, _vts: 1678090010121 }
//  on=
// Object { input: input(event) }
//   ... >
//   at <Anonymous conf=
// Object { __config__: {…}, __slot__: {…}, style: {}, size: "medium", disabled: false, __vModel__: "wechat-app-cash-status" }
//  on=
// Object { input: input(event) }
//  onInput=fn<onInput> >
//   at <ElFormItem label-width=null prop="wechat-app-cash-status" label="微信提现开启状态" >
//   at <ElCol span=24 >
//   at <ElForm size="small" label-position="top" disabled=false  ... >
//   at <ElRow gutter=15 onSubmit=fn >
//   at <Parser key=0 form-conf=
// Object { formRef: "elForm", formModel: "formData", size: "medium", labelPosition: "top", labelWidth: 150, formRules: "rules", gutter: 15, disabled: false, span: 24, formBtns: true, … }
//  onSubmit=fn  ... >
//   at <ElTabPane label="微信支付模块配置" name="147" key=0 >
//   at <ElTabs modelValue="147" onUpdate:modelValue=fn class="demo-tabs"  ... >
//   at <ElTabPane label="支付配置" name=103 key=103 >
//   at <ElTabs modelValue=103 onUpdate:modelValue=fn class="demo-tabs"  ... >
//   at <Index>
//   at <VueAdminBox$1678089907374 onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref<
// Proxy { <target>: {…}, <handler>: {…} }
//  > key="/operation/setting" >
//   at <KeepAlive key=0 include=
// Array []
//  >
//   at <BaseTransition mode="out-in" appear=false persisted=false  ... >
//   at <Transition name="fade-transform" mode="out-in" >
//   at <RouterView>
//   at <ElMain>
//   at <ElContainer>
//   at <ElContainer style=
// Object { height: "100vh" }
//  >
//   at <Index onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref<
// Proxy { <target>: {…}, <handler>: {…} }
//  > >
//   at <RouterView>
//   at <ElConfigProvider locale=
// Object { name: "zh-cn", el: Proxy }
//  size="small" >
//   at <App> runtime-core.esm-bundler.js:40:16
