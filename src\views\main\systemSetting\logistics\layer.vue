<template>
  <Layer :layer="layer" @confirm="submit(ruleForm)" ref="layerDom">
    <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;"
      v-loading="pageloading">
      <el-form-item label="模板名称">
        <el-input v-model="form.name" placeholder="请输入模板名称"></el-input>
      </el-form-item>
      <el-form-item label="计费方式">
        <el-radio-group v-model="form.type">
          <el-radio :label="1">
            按件数
          </el-radio>
          <el-radio :label="2">
            按重量
          </el-radio>
          <el-radio :label="3">
            按体积
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="模板名称">
        <el-table :data="form.shippingTemplatesRegionRequestList" style="width: 100%">

          <el-table-column prop="title" label="可配送区域" align="center" width="200">
            <template #default="scope">
              <div>
                <span v-if="scope.row.cityId == 'all'">全国</span>
                <el-cascader v-model="scope.row.cityId" :options="cityList" :props="cityProps" collapse-tags v-else />

              </div>
            </template>
          </el-table-column>
          <el-table-column prop="first" align="center">
            <template #header>
              <span>首件</span>
              <span v-show="form.type == 2">重量（kg）</span>
              <span v-show="form.type == 3">体积（m³）</span>
            </template>
            <template #default="scope">
              <div>
                <el-input-number v-model="scope.row.first" :min="0" controls-position="right" />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="运费（元）" align="center">
            <template #default="scope">
              <div>
                <el-input-number v-model="scope.row.firstPrice" :min="0" controls-position="right" />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="renewal" align="center">
            <template #header>
              <span>续件</span>
              <span v-show="form.type == 2">重量（kg）</span>
              <span v-show="form.type == 3">体积（m³）</span>
            </template>
            <template #default="scope">
              <div>
                <el-input-number v-model="scope.row.renewal" :min="0" controls-position="right" />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="renewalPrice" label="续费（元）" align="center">
            <template #default="scope">
              <div>
                <el-input-number v-model="scope.row.renewalPrice" :min="0" controls-position="right" />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <div v-if="scope.row.cityId != 'all'">
                <el-button type="danger" @click="delDistribution(scope)">删除</el-button>
              </div>
            </template>
          </el-table-column>

        </el-table>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addDistribution"><el-icon>
            <EditPen />
          </el-icon>添加配送区域</el-button>

      </el-form-item>
      <el-form-item label="指定包邮">
        <el-radio-group v-model="form.appoint">
          <el-radio :label="false">
            关闭
          </el-radio>
          <el-radio :label="true">
            开启
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-table :data="form.shippingTemplatesFreeRequestList" style="width: 100%" v-show="form.appoint">
          <el-table-column prop="cityId" label="选择地区" align="center">
            <template #default="scope">
              <div>
                <el-cascader v-model="scope.row.cityId" :options="cityList" :props="cityProps" collapse-tags />
              </div>
            </template>

          </el-table-column>
          <el-table-column prop="number" align="center">
            <template #header>
              <span v-show="form.type == 1">包邮件数</span>
              <span v-show="form.type == 2">包邮重量（kg）</span>
              <span v-show="form.type == 3">包邮金额（元）</span>
            </template>
            <template #default="scope">
              <div>
                <el-input-number v-model="scope.row.number" :min="0" controls-position="right" />
              </div>
            </template>

          </el-table-column>
          <el-table-column prop="price" label="包邮金额（元）" align="center">
            <template #default="scope">
              <div>
                <el-input-number v-model="scope.row.price" :min="0" controls-position="right" />
              </div>
            </template>

          </el-table-column>

          <el-table-column label="操作">
            <template #default="scope">
              <div>
                <el-button type="danger" @click="delFreeShipping(scope)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item v-show="form.appoint">
        <el-button type="primary" @click="addFreeShipping"><el-icon>
            <EditPen />
          </el-icon>添加指定包邮区域</el-button>

      </el-form-item>
      <el-form-item label="排序">
        <el-input-number v-model="form.sort" :min="0" />
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { ElFormItemContext } from 'element-plus/lib/el-form/src/token'
import {  ref, reactive,defineProps } from 'vue'
import { getCityTree, saveLogistics, updateLogistics, getLogisticsTemplate, getLogisticsTemplateFree } from '@/api/logistics'
import Layer from '@/components/layer/index.vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import type { LogisticsParams } from '@/type/logistics.type'

const emit = defineEmits(['getTableData'])
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})

    const ruleForm: Ref<ElFormItemContext | null> = ref(null)
    const layerDom: Ref<LayerType | null> = ref(null)
    const form = ref<any>({
      id:0,
      name: '',
      type: 1,
      sort: 0,
      appoint: false,
      shippingTemplatesRegionRequestList: [{
        cityId: 'all', title: '全国', first: 0, firstPrice: 0, renewal: 0, renewalPrice: 0
      }],
      shippingTemplatesFreeRequestList: []
    })

    const rules = {
      name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
    }
    const pageloading = ref(false)
    init()

    const cityList = ref()
    async function init() { // 用于判断新增还是编辑功能

      getCityTree().then(res => {
        cityList.value = res

      })
      if (props.layer.row) {
        pageloading.value = true
        form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
        form.value['shippingTemplatesRegionRequestList'] = []
        form.value['shippingTemplatesFreeRequestList'] = []
        await getLogisticsTemplate({ tempId: form.value.id }).then(res => {
          let data = res
          data.forEach((item) => {
            let obj = item
            if (data.length == 1 || item.title == '全国') {
              obj.cityId = 'all'
              form.value.shippingTemplatesRegionRequestList.push(obj)
            } else {
              let arr = (item.cityId as string).split(',').map(Number)
              obj.cityId = arr
              form.value.shippingTemplatesRegionRequestList.push(obj)
            }

          });
        })
        if (form.value.appoint) {
          await getLogisticsTemplateFree({ tempId: form.value.id }).then(res => {
            let data = res
            data.forEach(item => {
              let obj = item
              let arr = (obj.cityId as string).split(',').map(Number)
              item.cityId = arr
              form.value.shippingTemplatesFreeRequestList.push(obj)
            });

          })
        }
        pageloading.value = false
      } else {

      }

    }
    //添加配送区域
    function addDistribution() {
      let obj = {
        cityId: '',
        title: '',
        first: 0, firstPrice: 0, renewal: 0, renewalPrice: 0
      }
      form.value.shippingTemplatesRegionRequestList.push(obj)
    }
    //删除配送区域
    function delDistribution(scope: any) {
      form.value.shippingTemplatesRegionRequestList.splice(scope.$index, 1)
    }
    function delFreeShipping(scope: any) {
      form.value.shippingTemplatesFreeRequestList.splice(scope.$index, 1)
    }
    const cityProps = reactive({
      label: 'name',
      children: 'child',
      value: 'cityId',
      multiple: true,
      emitPath: false
    })
    function addFreeShipping() {
      let obj = {
        cityId: '',
        title: '',
        number: 0,
        price: 0
      }
      form.value.shippingTemplatesFreeRequestList.push(obj)
    }

    const submit = async (formEl: FormInstance | undefined) => {
      if (!formEl) return
      await formEl.validate((valid, fields) => {
        if (valid) {
          pageloading.value = true

          if (form.value.shippingTemplatesRegionRequestList.length > 1) {
              form.value.shippingTemplatesRegionRequestList.map((item:any) => {
                item.cityId = item.cityId.toString()
                return item
              })  }
              if (form.value.appoint) {
                form.value.shippingTemplatesFreeRequestList.map((item:any) => {
                  item.cityId = item.cityId.toString()
                  return item
                })
              }else{
                form.value.shippingTemplatesFreeRequestList=[]
              }

          let params = form.value
          if (props.layer.row) {
            updateForm(params)
          } else {
            addForm(params)
          }
        } else {
          console.log('error submit!', fields)
        }
      })
    }

    // 新增提交事件
    function addForm(params: LogisticsParams) {

      saveLogistics(params)
        .then(res => {
          ElMessage({
            type: 'success',
            message: '新增成功'
          })
          emit('getTableData', true)
          layerDom.value && layerDom.value.close()
        }).finally(() => {
        })
      }

      // 编辑提交事件
    function updateForm(params: LogisticsParams) {
      updateLogistics(params)
        .then(res => {
          ElMessage({
            type: 'success',
            message: '编辑成功'
          })
          emit('getTableData', false)
          layerDom.value && layerDom.value.close()
        })
    }



</script>

<style lang="scss" scoped></style>