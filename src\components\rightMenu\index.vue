<template>
    <div v-contextmenu:contextmenu v-if="type == 1">{{ uid }}</div>
    <span v-else-if="type == 2" v-contextmenu:contextmenu>
        <el-image :style="{ 'width': imgData.width + 'px', 'height': imgData.height + 'px' }" :src="picurl + imgData.imgurl"
            :hide-on-click-modal="true" />
    </span>
    <v-contextmenu ref="contextmenu">
        <v-contextmenu-item @click="showUserInfo" v-if="type == 1">查看</v-contextmenu-item>
        <v-contextmenu-item @click="showImg" v-else-if="type == 2">预览</v-contextmenu-item>
    </v-contextmenu>
    <getUserInfo v-if="layer.show" :layer="layer"></getUserInfo>
    <el-image-viewer @close="closeImgViewer" :url-list="[picurl + imgData.url]" v-if="showImageViewer" />
</template>
  
<script lang="ts" >
import { defineComponent, reactive, ref } from "vue";
import {
    directive,
    version,
    Contextmenu,
    ContextmenuItem,
    ContextmenuDivider,
    ContextmenuSubmenu,
    ContextmenuGroup,
} from "v-contextmenu";
import "v-contextmenu/dist/themes/default.css";
import getUserInfo from '@/components/getInfoById/index.vue'
import imagePreview from '@/components/imagePreview/index.vue'
import { getPicUrl } from '@/utils/index'
export default defineComponent({
    name: "ExampleSimple",
    components: {
        [Contextmenu.name]: Contextmenu,
        [ContextmenuItem.name]: ContextmenuItem,
        [ContextmenuDivider.name]: ContextmenuDivider,
        [ContextmenuSubmenu.name]: ContextmenuSubmenu,
        [ContextmenuGroup.name]: ContextmenuGroup,
        getUserInfo,
        imagePreview
    },
    directives: {
        contextmenu: directive,
    },
    props: {
        type: { type: Number, default: 1 }, //1是用户信息，2是图片预览
        uid: { type: Number, }, //用户id
        imgData: {
            type: Object,
            default: () => {
                return {
                    url: '', //图片地址
                    width: 50, //图片宽度
                    height: 50 //图片高度
                }
            }
        }
    },
    setup(props) {
        
        const picurl = getPicUrl()
        const showUserInfo = () => {
            layer.show = true
        }
        const showImg = () => {
            showImageViewer.value = true
        }
        const layer = reactive({
            show: false,
            id: props.uid
        })
        const showImageViewer = ref(false)
        const closeImgViewer = () => {
            showImageViewer.value = false
        }
        return {
            picurl,
            showImg,
            showImageViewer,
            closeImgViewer,
            layer,
            showUserInfo
        }
    }
});
</script>
  
<style scoped>
.box {
    display: flex;
    flex-direction: column;
    height: 100%;
}

h1 {
    text-align: left;
}

.wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    flex: 1;
    border: 1px dashed rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    background-color: rgba(35, 212, 206, 0.2);
}
</style>