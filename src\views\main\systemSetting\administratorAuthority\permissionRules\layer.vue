<template>
  <Layer :layer="layer" @confirm="submit(ruleForm)">
    <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;">
      <el-form-item label="上级菜单">
        <el-cascader v-model="form.pid" :options="tableData" :props="CascaderOp" />
      </el-form-item>
      <el-form-item label="菜单类型">
        <el-radio-group v-model="form.menuType">
          <el-radio label="M">菜单</el-radio>
          <el-radio label="C">目录</el-radio>
          <el-radio label="A">按钮</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="菜单图标" v-show="form.menuType === 'M'">
        <el-select v-model="form.icon" placeholder="请选择图标" ref="refSelect" clearable style="width:70%">
          <el-option v-for="(item, index) in optionsImg" :key="index" :value="item">
            <div class="option_box">
              <i class="option_img iconfont " :class="item"></i>
            </div>
          </el-option>
        </el-select>
        <i class="option_img iconfont " :class="form.icon"></i>
      </el-form-item>
      <el-form-item :label="form.menuType == 'M' ? '菜单名称' : form.menuType == 'C' ? '目录名称' : '按钮名称'" prop="name">
        <el-input v-model="form.name" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="显示顺序">
        <el-input-number v-model="form.sort" :min="1" controls-position="right" />
      </el-form-item>
      <el-form-item label="组件路径" v-show="form.menuType != 'A'" prop="component">
        <el-input v-model="form.component" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="权限规格" v-show="form.menuType != 'M'">
        <el-input v-model="form.perms" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="显示状态">
        <el-radio-group v-model="form.isShow">
          <el-radio :label="true">显示</el-radio>
          <el-radio :label="false">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import Layer from '@/components/layer/index.vue'
import { getMenuList, updateMenu, addMenu } from '@/api/systemSetting'
import { ElMessage } from 'element-plus'
import { optionsImg } from '@/utils/contants'
import type { FormInstance, FormRules } from 'element-plus'
import type { MenuListRes } from '@/type/systemSetting.type'
import {getTree} from '../permissionRules/index.vue'
import type {MenuInfoParams} from '@/type/systemSetting.type'
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const ruleForm = ref<FormInstance>()

const emit = defineEmits(['getTableData'])
let form = ref({
  name: '',
  component: '',
  isShow: true,
  sort: 1,
  menuType: 'M',
  perms: '',
  pid: 0,
  icon: ''
})
const rules = reactive<FormRules>(
  {
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  }
)
const tableData = ref<MenuListRes[]>([])
const CascaderOp = {
  checkStrictly: true,
  value: 'id',
  label: 'name',
  children: 'childList'
}

init()
function init() { // 用于判断新增还是编辑功能
  //获取菜单
  getMenuList({}).then(res => {
    tableData.value = getTree(res)
    tableData.value.unshift({ name: '主菜单', id: 0 })

  })
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转            
  } else {
  }
}


const submit = async (formEl: FormInstance | undefined) => {
  if (form.value.menuType !== 'A') {
    // 如果需要校验，设置component的校验规则
    rules.component = [{ required: true, message: '请输入组件路径', trigger: 'blur' }];
  } else {
    // 如果不需要校验，清空component的校验规则
    rules.component = [];
  }
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      let params: any = form.value
      if (params.menuType != 'M') {
        params.icon = ''
      }
      if (params.pid instanceof Array) {
        params.pid = params.pid.pop()
      }
      if (props.layer.row) {
        updateForm(params)
      } else {
        addForm(params)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 新增提交事件
function addForm(params: MenuInfoParams) {
  addMenu(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功'
      })
      props.layer.show = false
      emit('getTableData', true)
    })
}

function  // 编辑提交事件
  updateForm(params: MenuInfoParams) {
  updateMenu(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '编辑成功'
      })
      props.layer.show = false
      emit('getTableData', true)
    })
}
</script>

<style lang="scss" scoped>
.option_box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.option_img {
  margin-left: 7px;
}
</style>