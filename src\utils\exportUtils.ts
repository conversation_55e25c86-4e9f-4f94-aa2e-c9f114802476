/// <reference types="vite/client" />
import { ElMessage } from 'element-plus'
import axios from 'axios'
import store from '@/store'

/**
 * 导出配置接口
 */
export interface ExportConfig {
  /** API端点路径 */
  apiPath: string
  /** 请求参数 */
  params: Record<string, any>
  /** 文件名（不包含扩展名） */
  fileName: string
  /** 是否过滤空值参数，默认为 true */
  filterEmptyParams?: boolean
  /** 自定义文件名生成函数 */
  generateFileName?: (baseFileName: string) => string
  /** 成功回调 */
  onSuccess?: () => void
  /** 失败回调 */
  onError?: (error: any) => void
}

/**
 * 默认文件名生成函数 - 添加时间戳
 */
const defaultFileNameGenerator = (baseFileName: string): string => {
  const now = new Date()
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`
  return `${baseFileName}_${dateStr}.xlsx`
}

/**
 * 过滤空值参数
 */
const filterEmptyValues = (params: Record<string, any>): Record<string, any> => {
  const filtered: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(params)) {
    // 保留有效值：非null、非undefined、非空字符串、数字0也保留
    if (value !== null && value !== undefined && value !== '') {
      // 对于数字类型，0也是有效值
      if (typeof value === 'number' || (typeof value === 'string' && value.trim() !== '')) {
        filtered[key] = value
      } else if (Array.isArray(value) && value.length > 0) {
        filtered[key] = value
      } else if (typeof value === 'object' && Object.keys(value).length > 0) {
        filtered[key] = value
      }
    }
  }
  
  return filtered
}

/**
 * 通用导出函数
 * @param config 导出配置
 * @returns Promise<boolean> 导出是否成功
 */
export const exportData = async (config: ExportConfig): Promise<boolean> => {
  const {
    apiPath,
    params,
    fileName,
    filterEmptyParams = true,
    generateFileName = defaultFileNameGenerator,
    onSuccess,
    onError
  } = config

  try {
    const baseURL = import.meta.env.VITE_DOWNLOAD_URL as string
    const url = baseURL + apiPath

    // 处理请求参数
    const requestParams = filterEmptyParams ? filterEmptyValues(params) : params

    // 发送请求
    const response = await axios({
      method: 'post',
      url: url,
      responseType: 'blob',
      data: requestParams,
      headers: {
        Authorization: 'Bearer ' + store.getters['user/token'],
        adminToken: store.getters['user/token'],
        'Content-Type': 'application/json',
      },
    })

    // 处理返回的文件流
    const blob = new Blob([response.data], {
      type: 'application/vnd.ms-excel',
    })

    // 检查是否返回错误信息
    if (response.data.type === 'application/json') {
      const fileReader = new FileReader()
      fileReader.readAsText(blob, 'utf-8')
      
      return new Promise((resolve, reject) => {
        fileReader.onload = function () {
          try {
            const result = JSON.parse(fileReader.result as string)
            const errorMsg = result.message || '导出失败'
            ElMessage({
              message: errorMsg,
              type: 'error',
              duration: 3 * 1000,
            })
            onError?.(new Error(errorMsg))
            reject(new Error(errorMsg))
          } catch (parseError) {
            const errorMsg = '解析错误响应失败'
            ElMessage.error(errorMsg)
            onError?.(parseError)
            reject(parseError)
          }
        }
      })
    }

    // 生成文件名并下载
    const finalFileName = generateFileName(fileName)
    const downloadElement = document.createElement('a')
    const href = window.URL.createObjectURL(blob)
    
    downloadElement.href = href
    downloadElement.download = finalFileName
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement)
    window.URL.revokeObjectURL(href)

    // 成功回调
    onSuccess?.()
    
    return true

  } catch (error: any) {
    console.error('导出失败:', error)
    const errorMsg = error.message || '导出失败'
    ElMessage.error(errorMsg)
    onError?.(error)
    return false
  }
}

/**
 * 带加载状态的导出函数
 * @param config 导出配置
 * @param loadingRef 加载状态的响应式引用
 * @returns Promise<boolean> 导出是否成功
 */
export const exportDataWithLoading = async (
  config: ExportConfig,
  loadingRef: { value: boolean }
): Promise<boolean> => {
  loadingRef.value = true
  
  try {
    const result = await exportData(config)
    return result
  } finally {
    loadingRef.value = false
  }
}

/**
 * 预定义的导出配置工厂函数
 */
export const createExportConfig = {
  /**
   * 产品库存记录导出
   */
  productStock: (params: Record<string, any>): ExportConfig => ({
    apiPath: '/api/admin/product/stock/excelOutput',
    params,
    fileName: '产品库存记录',
    filterEmptyParams: true,
  }),

  /**
   * 原料库存记录导出
   */
  materialStock: (params: Record<string, any>): ExportConfig => ({
    apiPath: '/api/raw/material/stockLog/excelOutput',
    params,
    fileName: '原料库存记录',
    filterEmptyParams: true,
  }),

  /**
   * 订单导出
   */
  purchaseOrder: (orderIds: number[], orderNo?: string): ExportConfig => ({
    apiPath: '/api/admin/stockOrder/excelPurchaseOrder',
    params: { orderIds },
    fileName: orderNo ? `货单_${orderNo}` : '货单',
    filterEmptyParams: false,
    generateFileName: (baseFileName: string) => `${baseFileName}.xlsx`,
  }),

  /**
   * 发货统计导出
   */
  orderStatistics: (params: Record<string, any>): ExportConfig => ({
    apiPath: '/api/admin/stockOrder/orderStatic/excelOutput',
    params,
    fileName: '发货统计',
    filterEmptyParams: true,
  }),
}

/**
 * 导出防抖函数配置接口
 */
export interface ExportDebounceConfig {
  /** 防抖延迟时间，默认 1000ms */
  delay?: number
  /** 最小时间间隔限制，默认 2000ms */
  minInterval?: number
  /** 是否在首次调用时立即执行，默认 false */
  immediate?: boolean
}

/**
 * 导出按钮专用防抖函数
 *
 * 专门为导出按钮设计的防抖函数，具备以下特性：
 * - 防抖延迟：防止短时间内重复点击
 * - 最小时间间隔：确保两次导出操作之间有足够的间隔
 * - 与现有 loading 状态兼容
 * - 支持 TypeScript 类型安全
 *
 * @param fn 要执行的导出函数
 * @param config 防抖配置选项
 * @returns 防抖后的函数
 *
 * @example
 * ```typescript
 * // 基础用法
 * const debouncedExport = exportDebounce(async () => {
 *   await exportDataWithLoading(config, loadingRef)
 * })
 *
 * // 自定义配置
 * const debouncedExport = exportDebounce(
 *   async () => {
 *     await exportDataWithLoading(config, loadingRef)
 *   },
 *   {
 *     delay: 800,        // 防抖延迟 800ms
 *     minInterval: 3000, // 最小间隔 3秒
 *     immediate: false   // 不立即执行
 *   }
 * )
 *
 * // 在 Vue 组件中使用
 * const handleExport = exportDebounce(async () => {
 *   const config = createExportConfig.productStock(searchParams.value)
 *   await exportDataWithLoading(config, exportLoading)
 * })
 * ```
 */
export const exportDebounce = <T extends (...args: any[]) => any>(
  fn: T,
  config: ExportDebounceConfig = {}
): ((...args: Parameters<T>) => void) => {
  const {
    delay = 1000,
    minInterval = 2000,
    immediate = true
  } = config

  let timer: ReturnType<typeof setTimeout> | null = null
  let lastExecuteTime = 0
  let isFirstCall = true

  return function (this: any, ...args: Parameters<T>): void {
    const now = Date.now()
    
    // 检查最小时间间隔（除了首次调用）
    if (!isFirstCall && now - lastExecuteTime < minInterval) {
      console.warn(`导出操作过于频繁，请等待 ${Math.ceil((minInterval - (now - lastExecuteTime)) / 1000)} 秒后再试`)
      return
    }

    // 清除之前的定时器
    if (timer) {
      clearTimeout(timer)
      timer = null
    }

    // 立即执行逻辑（仅首次且配置了 immediate）
    if (immediate && isFirstCall) {
      isFirstCall = false
      lastExecuteTime = now
      fn.apply(this, args)
      return
    }

    // 设置防抖定时器
    timer = setTimeout(() => {
      isFirstCall = false
      lastExecuteTime = Date.now()
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

/**
 * 创建导出按钮防抖处理器的工厂函数
 *
 * 为特定的导出场景创建预配置的防抖函数
 *
 * @param exportConfig 导出配置
 * @param loadingRef 加载状态引用
 * @param debounceConfig 防抖配置
 * @returns 防抖后的导出处理函数
 *
 * @example
 * ```typescript
 * // 在 Vue 组件的 setup 中使用
 * const exportLoading = ref(false)
 * const searchParams = ref({})
 *
 * const handleExport = createExportHandler(
 *   () => createExportConfig.productStock(searchParams.value),
 *   exportLoading,
 *   { delay: 800, minInterval: 2500 }
 * )
 *
 * // 在模板中绑定
 * // <el-button @click="handleExport" :loading="exportLoading">导出</el-button>
 * ```
 */
export const createExportHandler = (
  exportConfig: () => ExportConfig,
  loadingRef: { value: boolean },
  debounceConfig: ExportDebounceConfig = {}
) => {
  return exportDebounce(async () => {
    try {
      const config = exportConfig()
      await exportDataWithLoading(config, loadingRef)
    } catch (error) {
      console.error('导出处理失败:', error)
    }
  }, debounceConfig)
}

/**
 * 预定义的导出防抖配置
 */
export const exportDebouncePresets = {
  /** 默认配置：1秒防抖，2秒最小间隔 */
  default: { delay: 1000, minInterval: 2000 },
  
  /** 快速配置：500ms防抖，1.5秒最小间隔 */
  fast: { delay: 500, minInterval: 1500 },
  
  /** 保守配置：1.5秒防抖，3秒最小间隔 */
  conservative: { delay: 1500, minInterval: 3000 },
  
  /** 立即执行配置：首次立即执行，后续1秒防抖，2秒最小间隔 */
  immediate: { delay: 1000, minInterval: 2000, immediate: true }
} as const