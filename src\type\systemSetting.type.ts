/**@name 创建角色请求体 */
export interface AddRoleParams {
  /** @name  角色id，新增时可不填  */
  id?: number
  /** @name  真实姓名  */
  roleName: string
  /** @name  状态  */
  status: boolean
  /** @name  权限字符串(英文逗号拼接)  */
  rules: string | string[]
}
/**@name 角色列表响应体 */
export interface RoleListRes {
  /** @name  创建时间  */
  createTime?: string
  /** @name  角色id  */
  id: number
  /** @name   level*/
  level?: number
  /** @name   角色名称*/
  roleName?: string
  /** @name   角色权限*/
  rules?: string
  /** @name   状态*/
  status?: boolean
  /** @name  更新时间  */
  updateTime?: string
  /**@name 加载 */
  loading?:boolean
}
/**@name 角色详情响应体 */
export interface  RoleListParams{
  /** 名称 */
  roleName?:string
  page?: number
  /*** @name 每页条数*/
  limit?: number
}
/**@name 角色详情响应体 */
export interface RoleInfoRes extends RoleListRes {
  menuList: MenuRes[]
}
/**@name 菜单列表详情 */
interface MenuRes {
  /** @name  更新时间  */
  checked?: boolean
  /** @name  子对象列表  */
  childList?: MenuRes[]
  /** @name  图标  */
  icon?: string
  /** @name  菜单ID  */
  id?: number
  /** @name  父级ID  */
  pid?: number
  /** @name  菜单名称  */
  name?: string
  /** @name  排序  */
  sort?: number
    /**@name 加载 */
    loading?:boolean
}

/**@name  管理员详情  */
export interface AdminListRes {
  account?: string
  addTime?: number
  id: number
  /**
   * 是否接收短信
   */
  isSms?: boolean
  lastIp?: string
  lastTime?: Date
  level?: number
  loginCount?: number
  /**
   * 权限标识数组
   */
  permissionsList?: string[]
  /**
   * 手机号码
   */
  phone?: string
  realName?: string
  roleNames?: string
  roles?: string
  status?: boolean
      /**@name 加载 */
      loading?:boolean
  
}
/**@name  管理员新增请求体  */
export interface AdminAddParams {
  /**
   *@name 管理员id，编辑时必填
   */
  id?: number
  /**
   *@name 后台管理员账号
   */
  account: string
  /**
   *@name  手机号
   */
  phone: string
  /**
   *@name  后台管理员密码
   */
  pwd: string
  /**
   *@name  后台管理员姓名
   */
  realName: string
  /**
   *@name  后台管理员角色(menus_id)
   */
  roles: string
  /**
   *@name  后台管理员状态 1有效0无效
   */
  status: boolean
  /**
   *@name  后台区域管理
   */
  areaIds: number[] | string[]

}
/**@name   规则列表请求体 */

export interface MenuListParams{
    /*** @name   菜单名称*/
    name?: string
      /*** @name 菜单类型*/
      menuType?: string
      page?: number
      /*** @name 每页条数*/
      limit?: number
  
}
/**@name   规则添加/编辑请求体*/
export interface MenuInfoParams {
 /**
   *@name 组件路径
   */
   component?: string
   /**
    *@name icon
    */
   icon?: string
   id?: number
   /**
    *@name 显示状态
    */
   isShow?: boolean
   /**
    *@name 类型，M-目录，C-菜单，A-按钮
    */
   menuType?: string
   /**
    * 名称
    */
   name?: string
   /**
    *@name 权限标识
    */
   perms?: string
   /**
    *@name 父级ID
    */
   pid?: number
   /**
    *@name 排序
    */
   sort?: number

}
/**@name   规则列表响应体 */
export interface MenuListRes extends MenuInfoParams {
  /**
   *@name 创建时间
   */
  createTime?: Date

    /**@name 加载 */
    loading?:boolean

}

