<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>&#x7b80;&#x4ecb;</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 align="center">vue-admin-box</h1>
<p align="center">
    <a href="https://github.com/vuejs/vue-next">
        <img src="https://img.shields.io/badge/vue3-3.0.5-brightgreen.svg" alt="vue">
    </a>
    <a href="https://github.com/element-plus/element-plus">
        <img src="https://img.shields.io/badge/elementPlus-1.0.2beta.42-brightgreen.svg" alt="element-plus">
    </a>
    <a href="https://github.com/vitejs/vite">
        <img src="https://img.shields.io/badge/vite-2.2.3-brightgreen.svg" alt="vite">
    </a>
    <a href="https://github.com/microsoft/TypeScript">
        <img src="https://img.shields.io/badge/typescript-4.1.3-brightgreen.svg" alt="typescript">
    </a>
    <a href="https://github.com/hsiangleev/element-plus-admin/blob/master/LICENSE">
        <img src="https://img.shields.io/github/license/mashape/apistatus.svg" alt="license">
    </a>
</p>
<p><a href="./README.html">English</a> | 简体中文</p>
<h2 id="简介">简介</h2>
<ul>
<li><a href="./VERSION.html">更新日志</a></li>
<li>经过三个多月的迭代，于2021年8月10日，1.0版本正式发布，并附四个基础模板供大家使用</li>
<li>此开源项目为个人开发，不限制任何商业使用和个人研究，使用之前请先点个Star对我进行鼓励</li>
<li>利用此开源项目参与的一切违法、色情相关的活动均与本源码无关，请勿以身示法</li>
<li>QQ交流群：735838842，有问题群里提，可及时解决</li>
</ul>
<h2 id="预览">预览</h2>
<ul>
<li><a href="https://cmdparkour.gitee.io/vue-admin-box/">demo国内</a></li>
<li><a href="https://cmdparkour.github.io/vue-admin-box/dist/">demo外国</a></li>
<li><a href="https://github.com/cmdparkour/vue-admin-box">github地址</a></li>
<li><a href="https://gitee.com/cmdparkour/vue-admin-box">码云地址</a></li>
<li>国内推荐使用yarn或者cnpm进行安装，npm安装容易产生问题</li>
</ul>
<h2 id="基础模板">基础模板</h2>
<p>共四个基础模板，均适合从零研发使用，可在<a href="https://github.com/cmdparkour/vue-admin-box-template">github</a>、<a href="https://gitee.com/cmdparkour/vue-admin-box-template">码云</a>仓库中直接查看，模板如下所示</p>
<ol>
<li>template-ts-i18n 基础模板，内含ts语法+国际化配置 <a href="http://vue-admin-box-template.51weblove.com/ts-i18n/">demo</a></li>
<li>template-ts 基础模板，只含ts语法，国际化已去除 <a href="http://vue-admin-box-template.51weblove.com/ts">demo</a></li>
<li>template-js-i18n 基础模板，js语法 + 国际化配置 <a href="http://vue-admin-box-template.51weblove.com/js-i18n">demo</a></li>
<li>template-js 基础模板，js语法，国际化已去除 <a href="http://vue-admin-box-template.51weblove.com/js">demo</a></li>
</ol>
<h2 id="介绍">介绍</h2>
<p>vue-admin-box是一个免费并且开源的中后台管理系统模板。使用最新版本的vue3+vite+element-plus开发而成，目的是为了解决通用型的业务中后台系统复杂的配置。</p>
<h4 id="特色功能">特色功能</h4>
<ul>
<li>适合中后台开发的路由配置、状态管理机制（状态默认支持本地存储）、已封装完善的axios及api管理机制</li>
<li>极方便扩展的主题配置功能，默认支持三种典型的中后台风格</li>
<li>简易配置的页面缓存功能，只需配置noCache属性，无需配置其他的任何属性，如组件名称，路由名称等等很多框架需要配置的东西</li>
<li>典型增删改查的三种业务表格，详情请查看“页面栏目”内的“业务表格”、“分类联动表格”、“树联动表格”</li>
<li>无路由跳转的刷新功能，支持缓存页面刷新，目前了解的多数框架都不支持缓存页面的刷新</li>
<li>方便扩展的国际化解决方案，并提供了两套非国际化的基础模板和两套国际化的基础模板（ts版本/js版本）</li>
<li>手写版本的各类自定义指令</li>
<li>已经过多个中后台业务检验过的表格公用组件及弹窗公用组件，详情请查看“页面栏目”内的“业务表格”、“分类联动表格”、“树联动表格”</li>
</ul>
<h4 id="主要技术栈">主要技术栈</h4>
<ul>
<li>MVVM框架：vue v3</li>
<li>工程化管理：vite v2</li>
<li>UI框架：element-plus</li>
<li>路由管理：vue-router v4</li>
<li>状态管理：vuex v4</li>
<li>数据请求：axios</li>
<li>实用工具库：@vueuse/core</li>
</ul>
<h2 id="使用">使用</h2>
<ol>
<li>
<p>获取源码资源包</p>
<pre><code>git clone https://github.com/cmdparkour/vue-admin-box.git
</code></pre>
</li>
<li>
<p>安装依赖，国内推荐使用cnpm或tyarn，国外推荐使用npm或yarn</p>
<pre><code>npm install
</code></pre>
</li>
<li>
<p>运行</p>
<pre><code>npm run dev 或 npm run start
</code></pre>
</li>
<li>
<p>打包</p>
<pre><code>npm run build
</code></pre>
</li>
</ol>
<h2 id="partner-project">Partner project</h2>
<ul>
<li><a href="https://github.com/yirius/thinker-admin-box">Thinker-Aamin-Box - vue-admin-box的springboot后台，可使用java代码便捷创建vue界面</a></li>
<li><a href="http://h5.dooring.cn">H5-dooring - 一款h5端页面可视化编辑器</a></li>
<li><a href="https://github.com/lgf196/ant-simple-pro">ant-simple-pro - 一款支持vue3.0，react，angular，typescript等多框架支持的中台前端解决方案</a></li>
<li><a href="https://github.com/vbenjs/vue-vben-admin">vue-vben-admin - 使用了最新的vue3,vite2,TypeScript等主流技术开发，开箱即用的中后台前端解决方案</a></li>
</ul>
<h2 id="效果预览">效果预览</h2>
<p align="center">
    <img src="http://blog.51weblove.com/wp-content/uploads/2021/08/QQ截图20210810174824.png">
    <img src="http://blog.51weblove.com/wp-content/uploads/2021/08/QQ截图20210810174848.png">
    <img src="http://blog.51weblove.com/wp-content/uploads/2021/08/QQ截图20210810174923.png">
    <img src="http://blog.51weblove.com/wp-content/uploads/2021/08/QQ截图20210810174940.png">
    <img src="http://blog.51weblove.com/wp-content/uploads/2021/08/QQ截图20210810175009.png">
</p>

            
            
        </body>
        </html>