// import request from '@/utils/request'
import request from "@/utils/system/request"
import type { ResponseList, ResponseMessage } from "@/type/index.type"
import type {
  AddRoleParams,
  RoleListRes,
  RoleListParams,
  RoleInfoRes,
  AdminListRes,
  AdminAddParams,
  MenuListRes,
  MenuListParams,
  MenuInfoParams
} from "@/type/systemSetting.type"

/**@name 新增角色   */
export function addRole(params: AddRoleParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/role/save",
    method: "POST",
    data: params,
  })
}
/**@name 修改角色身份状态   */
export function updateRoleStatus(params: {
  id: number
  status: string
}): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/role/updateStatus",
    method: "get",
    params: params,
  })
}
/**@name 修改角色   */
export function updateRole(params: AddRoleParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/role/update",
    method: "post",
    data: params,
  })
}
/**@name 删除角色   */
export function delRole(params: { id: number }): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/role/delete",
    method: "GET",
    params: params,
  })
}

/**@name 角色详情   */
export function getRoleInfo(params: number): Promise<RoleInfoRes> {
  return request({
    url: `/api/admin/system/role/info/${params}`,
    method: "GET",
  })
}

/**@name 角色分页列表   */
export function getRoleList(params:RoleListParams ): Promise<ResponseList<RoleListRes[]>> {
  return request({
    url: "/api/admin/system/role/list",
    method: "get",
    params: params,
  })
}
/**@name 管理员分页列表   */
export function getAdminList(params: {
  realName?: string
}): Promise<ResponseList<AdminListRes[]>> {
  return request({
    url: "/api/admin/system/admin/list",
    method: "get",
    params: params,
  })
}
/**@name 添加管理员   */
export function saveAdmin(data: AdminAddParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/admin/save",
    method: "post",
    data: data,
  })
}
/**@name 修改管理员   */
export function updateAdmin(data: AdminAddParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/admin/update",
    method: "post",
    data: data,
  })
}
/**@name 修改管理员状态   */
export function updateAdminStatus(data: {
  id: number
  status: string
}): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/admin/updateStatus",
    method: "get",
    params: data,
  })
}
/**@name 删除管理员   */
export function delAdmin(data: { id: number }): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/admin/delete",
    method: "get",
    params: data,
  })
}
/**@name 权限分页列表   */
export function getMenuList(params: MenuListParams): Promise<MenuListRes[]> {
  return request({
    url: "/api/admin/system/menu/cache/tree",
    method: "get",
    params: params,
  })
}
/**@name 添加规则菜单   */
export function addMenu(params: MenuInfoParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/menu/add",
    method: "post",
    data: params,
  })
}
/**@name 修改规则菜单   */
export function updateMenu(params: MenuInfoParams): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/menu/update",
    method: "post",
    data: params,
  })
}
/**@name 删除规则菜单   */
export function deleteMenu(params: number): Promise<ResponseMessage> {
  return request({
    url: "/api/admin/system/menu/delete/" + params,
    method: "post",
  })
}
