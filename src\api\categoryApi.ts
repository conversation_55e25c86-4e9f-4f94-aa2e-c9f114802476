import request from '@/utils/system/request'
import type {
  CategroyParms,
  TreeCategroyParms,
  updateCategroyParms,
  infoCategroyRes,
  TreeCategroyRes,
} from '@/type/category.type'
import type { ResponseList, ResponseMessage } from '@/type/index.type'
/**
 * 新增分类
 * @param pram
 */
export function addCategroy(pram: CategroyParms): Promise<ResponseMessage> {
  const data = {
    extra: pram.extra,
    name: pram.name,
    pid: pram.pid,
    sort: pram.sort,
    status: pram.status,
    type: pram.type,
    url: pram.url,
  }

  return request({
    url: '/api/admin/category/save',
    method: 'POST',
    params: data,
  })
}

/**
 * 分类详情
 * @param pram
 */
export function infoCategroy(pram: { id: number }): Promise<infoCategroyRes[]> {
  const data = {
    id: pram.id,
  }
  return request<any>({
    url: '/api/admin/category/info',
    method: 'GET',
    params: data,
  })
}

/**
 * 删除分类
 * @param pram
 */
export function deleteCategroy(pram: {
  id: string | number
}): Promise<ResponseMessage> {
  const data = {
    id: pram.id,
  }
  return request({
    url: '/api/admin/category/delete',
    method: 'GET',
    params: data,
  })
}

/**
 * 分类数据tree数据
 * @param pram
 */
export function treeCategroy(
  pram: TreeCategroyParms
): Promise<TreeCategroyRes[]> {
  const data = {
    type: pram.type,
    status: pram.status,
    name: pram.name,
  }

  return request({
    url: '/api/admin/category/list/tree',
    method: 'GET',
    params: data,
  })
}

/**
 * 更新分类
 * @param pram
 */
export function updateCategroy(
  pram: updateCategroyParms
): Promise<ResponseMessage> {
  const data = {
    extra: pram.extra,
    name: pram.name,
    pid: pram.pid,
    sort: pram.sort,
    status: pram.status,
    type: pram.type,
    url: pram.url,
  }
  return request({
    url: `/api/admin/category/update/${pram.id}`,
    method: 'POST',
    data: data,
  })
}

/**
 * 修改 显示关闭状态
 * @param pram
 */
export function categroyUpdateStatus(id: number): Promise<ResponseMessage> {
  return request({
    url: `/api/admin/category/updateStatus/${id}`,
    method: 'GET',
  })
}
