<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        lazy
        v-loading="loading"
        :tree-props="{ children: 'child', hasChildren: 'hasChildren' }"
        :header-cell-style="{ fontWeight: 'bold' }"
        border
      >
        <el-table-column prop="id" label="编号" />
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="url" label="英文名称" />
        <el-table-column prop="extra" label="已关联表单">
          <template #default="scope">
            {{ scope.row.extra }}
            <el-button
              v-if="scope.row.extra"
              @click="showConfigLayer(scope.row.extra)"
            >
              查看表单
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="启用状态">
          <template #default="scope">
            <el-tag :type="scope.row.status ? '' : 'danger'">
              {{ scope.row.status ? '显示' : '隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button type="text" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              @click="configure(scope.row)"
              v-if="!scope.row.child"
            >
              关联表单
            </el-button>
            <el-popconfirm
              title="确定删除选中的数据吗？"
              @confirm="handleDel(scope.row.id)"
            >
              <template #reference>
                <el-button type="text">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
      <config-layer :layer="configLayer" v-if="configLayer.show" />
    </div>

    <el-dialog v-model="dialogTableVisible" title="选择已配置的表单">
      <el-input
        v-model="gridDatapage.keywords"
        placeholder="请输入表单名称"
        clearable
        @clear="getgridData"
        style="width: 30%; float: left"
      >
        <template #append>
          <el-button :icon="Search" @click="getgridData" />
        </template>
      </el-input>
      <span style="color: #f56c6c">注意：表单不能重复关联</span>
      <el-table
        :data="gridData"
        @current-change="handleChangeTable"
        highlight-current-row
      >
        <el-table-column property="id" label="id" width="100" />
        <el-table-column property="name" label="名称" width="200" />
        <el-table-column property="info" label="描述" />
        <el-table-column property="updateTime" label="更新时间" />
        <el-table-column>
          <template #default="scope">
            <el-button @click="showConfigLayer(scope.row.id)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="gridDatapage.page"
        :page-size="20"
        layout="total, prev, pager, next"
        :total="gridDatapage.total"
        @current-change="handleCurrentChange"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogTableVisible = false">取消</el-button>
          <el-button type="primary" @click="handFormSave">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { defineComponent, ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import {
    treeCategroy,
    updateCategroy,
    deleteCategroy,
  } from '@/api/categoryApi'
  import Layer from './layer.vue'
  import ConfigLayer from './configLayer.vue'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import Table from '@/components/table/index.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import { getFormConfigInfo, getFormConfigList } from '@/api/systemFormConfig'
  import type { TreeCategroyRes } from '@/type/category.type'
  // 存储搜索用的数据
  const query = reactive({
    type: 6,
    status: -1,
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  const configLayer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: false,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<TreeCategroyRes[]>([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    treeCategroy(params)
      .then(res => {
        let data = res
        data.forEach((d: TreeCategroyRes) => {
          d.loading = false
        })
        tableData.value = data
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: number) => {
    let params = {
      id: data,
    }
    deleteCategroy(params).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }
  // 编辑弹窗功能
  const handleEdit = (row: TreeCategroyRes) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }

  function getgridData() {
    getFormConfigList(gridDatapage.value).then(res => {
      gridData.value = res.list
      gridDatapage.value.total = res.total
    })
  }
  const rowVal = ref()
  const configure = (row: TreeCategroyRes) => {
    rowVal.value = row
    dialogTableVisible.value = true
    tableId.value = row.id
    getgridData()
  }
  const dialogTableVisible = ref(false)
  const gridData = ref()
  const formId = ref()
  const gridDatapage = ref({
    page: 1,
    limit: 20,
    total: 0,
    keywords: '',
  })
  const handleCurrentChange = (val: number) => {
    gridDatapage.value.page = val
    getgridData()
  }
  const tableId = ref()
  function handFormSave() {
    let data = {
      id: rowVal.value.id,
      name: rowVal.value.name,
      pid: rowVal.value.pid,
      sort: rowVal.value.sort,
      status: rowVal.value.status,
      extra: formId.value.id,
      url: rowVal.value.url,
      type: rowVal.value.type,
    }
    updateCategroy(data).then(res => {
      dialogTableVisible.value = false
      ElMessage({
        type: 'success',
        message: '修改成功',
      })
      getTableData(true)
    })
  }
  getTableData(true)
  const handleChangeTable = (id: number) => {
    formId.value = id
  }
  const showConfigLayer = (id: number) => {
    getFormConfigInfo({ id }).then(res => {
      configLayer.title = '查看数据'
      configLayer.row = res
      configLayer.show = true
      configLayer.width = '100%'
      configLayer.fullscreen = true
    })
  }
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
