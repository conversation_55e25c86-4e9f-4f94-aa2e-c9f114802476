<template>
  <Layer :layer="layer" @confirm="submit" ref="layerDom">

    <el-form :model="form" ref="ruleForm" :rules="rules" label-width="120px" style="margin-right:30px;">
      <el-form-item label="类型:">
        <el-radio-group v-model="form.targetType">
          <el-radio-button v-for="item in targetTypeList" :key="item.value" :label="item.value">{{ item.label
          }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="targetTypeList[form.targetType - 1].label">
        <el-input-number v-model="form.amount" :min="0" />

      </el-form-item>
      <el-form-item label="状态:">
        <el-radio-group v-model="form.type">
          <el-radio :label="1">增加</el-radio>
          <el-radio :label="2">减少</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </Layer>
</template>
<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { FormRules } from 'element-plus'
import { defineComponent, ref } from 'vue'
import Layer from '@/components/layer/index.vue'
import { updateFinance } from '@/api/userManage'
import { ElMessage } from 'element-plus'
import type {UpdateFinanceParams} from '@/type/userManage.type'
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
    const emit = defineEmits(['getTableData'])
    const ruleForm: Ref<FormRules | null> = ref(null)
    const layerDom: Ref<LayerType | null> = ref(null)
    const rules = {
      // amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
    }
    const  form = ref<UpdateFinanceParams>({
      uid: 0,
      amount: 0,
      type: 1,
      targetType: 1
    })
    init()
    function init() { // 用于判断新增还是编辑功能
      if (props.layer.row) {
        const { uid } = JSON.parse(JSON.stringify(props.layer.row))
        form.value.uid = uid
      }
    }
    const targetTypeList = [
      { value: 1, label: '余额' },
      { value: 2, label: '积分' },

    ]
    function submit() {
      let data = form.value;
      updateFinance(data).then(res => {
        ElMessage({
          type: 'success',
          message: '修改成功'
        })
        emit('getTableData', false)
        layerDom.value && layerDom.value.close()
      })
    }

</script>
<style lang="scss" scoped></style>