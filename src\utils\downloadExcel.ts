import { ElMessage } from 'element-plus'
import axios from 'axios'
import store from '@/store'
const baseURL = import.meta.env.VITE_DOWNLOAD_URL
function getNowDate() {
  var myDate = new Date()
  var year = myDate.getFullYear() //获取当前年
  var mon = myDate.getMonth() + 1 //获取当前月
  var date = myDate.getDate() //获取当前日
  var hours = myDate.getHours() //获取当前小时
  var minutes = myDate.getMinutes() //获取当前分钟
  var seconds = myDate.getSeconds() //获取当前秒
  var now =
    year + '-' + mon + '-' + date + ' ' + hours + ':' + minutes + ':' + seconds
  return now
}
export function downloadExcel(data: any, apiurl: string, name: string) {
  let url = baseURL + apiurl
  axios({
    method: 'get',
    url: url,
    responseType: 'blob',
    params: data,
    headers: {
      Authorization: 'Bearer ' + store.getters['user/token'],
      adminToken: store.getters['user/token'],
    },
  })
    .then(async res => {
      // 处理返回的文件流
      let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
      if (res.type === 'application/json') {
        const fileReader = new FileReader()
        fileReader.readAsText(blob, 'utf-8')
        fileReader.onload = function () {
          let msg = JSON.parse(fileReader.result).message
          ElMessage({
            message: msg,
            type: 'error',
            duration: 3 * 1000,
          })
        }
      } else {
        //获取fileName,截取content-disposition的filename；按=分割，取最后一个
        const fileName = name + getNowDate()
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) //创建下载的链接
        downloadElement.href = href
        downloadElement.download = fileName //下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() //点击下载
        document.body.removeChild(downloadElement) //下载完成移除元素
        window.URL.revokeObjectURL(href) //释放blob
        // const isLogin = await this.blobValidate(res);
        // if (isLogin) {
        // const blob = new Blob([res])
        // this.saveAs(blob, decodeURI(res.headers['download-filename']))
        // } else {
        //   Message.error('无效的会话，或者会话已过期，请重新登录。');
        // }
      }
    })
    .catch(err => {
      console.error(err)
      ElMessage({
        message: err.message,
        type: 'error',
        duration: 3 * 1000,
      })
    })
}

// 支持POST请求的文件下载函数
export function downloadExcelPost(data: any, apiurl: string, name: string) {
  let url = baseURL + apiurl
  axios({
    method: 'post',
    url: url,
    responseType: 'blob',
    data: data,
    headers: {
      Authorization: 'Bearer ' + store.getters['user/token'],
      adminToken: store.getters['user/token'],
      'Content-Type': 'application/json',
    },
  })
    .then(async res => {
      // 处理返回的文件流
      let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
      if (res.data.type === 'application/json') {
        const fileReader = new FileReader()
        fileReader.readAsText(blob, 'utf-8')
        fileReader.onload = function () {
          let msg = JSON.parse(fileReader.result as string).message
          ElMessage({
            message: msg,
            type: 'error',
            duration: 3 * 1000,
          })
        }
      } else {
        //获取fileName,截取content-disposition的filename；按=分割，取最后一个
        const fileName = name + '.xlsx'
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) //创建下载的链接
        downloadElement.href = href
        downloadElement.download = fileName //下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() //点击下载
        document.body.removeChild(downloadElement) //下载完成移除元素
        window.URL.revokeObjectURL(href) //释放blob
      }
    })
    .catch(err => {
      console.error(err)
      ElMessage({
        message: err.message,
        type: 'error',
        duration: 3 * 1000,
      })
    })
}
