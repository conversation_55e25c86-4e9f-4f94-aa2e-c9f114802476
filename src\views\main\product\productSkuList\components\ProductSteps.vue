<template>
  <div style="text-align: left">
    <el-steps
      :space="800"
      :active="activeIndex"
      align-center
      style="margin-bottom: 10px"
    >
      <el-step title="选择预约货品" :icon="activeIndex > 0 ? Check : ''" />
      <el-step :title="stepTitle" :icon="activeIndex > 1 ? Check : ''" />
      <el-step title="完成" :icon="activeIndex > 2 ? Check : ''" />
    </el-steps>
  </div>
</template>

<script lang="ts" setup>
  import { Check } from '@element-plus/icons-vue'

  defineProps<{
    activeIndex: number
    stepTitle: string
  }>()
</script>

<style lang="scss" scoped></style>
