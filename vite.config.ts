/*
 * @Author: luoxi
 * @Date: 2022-01-25 09:51:12
 * @LastEditors: luoxi
 * @LastEditTime: 2022-01-25 12:40:52
 * @FilePath: \vue-admin-box\vite.config.ts
 * @Description:
 */
import { ConfigEnv, UserConfigExport } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'

const pathResolve = (dir: string): any => {
  return resolve(__dirname, '.', dir)
}

const alias: Record<string, string> = {
  '@': pathResolve('src'),
}

/**
 * @description-en vite document address
 * @description-cn vite官网
 * https://vitejs.cn/config/ */
export default ({ command }: ConfigEnv): UserConfigExport => {
  return {
    base: './',
    resolve: {
      alias,
    },
    server: {
      port: 3111,
      host: '0.0.0.0',
      open: true,
      hmr: true,
      proxy: {
        // 代理配置
        '/dev':
          'https://www.fastmock.site/mock/48cab8545e64d93ff9ba66a87ad04f6b/',
      },
    },
    optimizeDeps: {
      include: ['xlsx', 'print-js'],
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            echarts: ['echarts'],
            xlsx: ['xlsx'],
            'print-js': ['print-js'],
          },
        },
      },
    },
    plugins: [
      UnoCSS(),
      vueJsx({
        // options are passed on to @vue/babel-plugin-jsx
      }),
      vue(),
    ],
  }
}
