<template>
  <Table
    ref="table"
    row-key="id"
    :page="page"
    :showSelection="false"
    @update:page="updatePage"
    v-loading="loading"
    :data="data"
    @getTableData="handleGetTableData"
    @selection-change="handleSelectionChange"
  >
    <el-table-column
      type="selection"
      reserve-selection
      align="center"
      width="80"
    />
    <el-table-column prop="id" label="id" align="center" width="80" />
    <el-table-column label="商品图" align="center">
      <template #default="scope">
        <imagePreview :imgurl="scope.row.image"></imagePreview>
      </template>
    </el-table-column>
    <el-table-column prop="productName" label="商品名称" align="center" />
    <el-table-column prop="attrValueNo" label="产品编码" align="center" />
    <el-table-column prop="suk" label="商品规格" align="center" />
    <el-table-column prop="unitGroupStr" label="库存" align="center" />
  </Table>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import type { Page } from '@/components/table/type'
  import type { ProductListRes } from '@/type/product.type'

  defineProps<{
    page: Page
    loading: boolean
    data: ProductListRes[]
  }>()

  const emit = defineEmits<{
    'update:page': [value: Page]
    getTableData: [init?: boolean]
    'selection-change': [selection: ProductListRes[]]
  }>()

  // 添加表格引用
  const table = ref()

  const updatePage = (value: Page) => {
    emit('update:page', value)
  }

  const handleGetTableData = (init?: boolean) => {
    emit('getTableData', init)
  }

  const handleSelectionChange = (selection: ProductListRes[]) => {
    emit('selection-change', selection)
  }

  // 暴露表格引用，供父组件使用
  defineExpose({
    table,
  })
</script>

<style lang="scss" scoped></style>
